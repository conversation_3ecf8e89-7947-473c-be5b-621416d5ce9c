/**
 * Jest Configuration für DSV Scraper - Node.js Version
 */
export default {
  // Test Environment
  testEnvironment: 'node',
  
  // Test Files - Focus on new modular architecture
  testMatch: [
    '**/tests/architecture/**/*.test.js'
  ],

  // Temporarily ignore legacy tests that need refactoring
  testPathIgnorePatterns: [
    '<rootDir>/node_modules/',
    '<rootDir>/coverage/',
    '<rootDir>/tests/utils/',
    '<rootDir>/tests/scrapers/',
    '<rootDir>/tests/web/',
    '<rootDir>/tests/scheduler/',
    '<rootDir>/tests/config.test.js'
  ],
  
  // Coverage - Disabled until tests are stable
  collectCoverage: false,
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  collectCoverageFrom: [
    'src/**/*.js',
    '!src/**/*.test.js',
    '!src/**/*.spec.js',
    '!src/frontend/public/**',
    '!src/frontend/views/**'
  ],
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70
    }
  },
  
  // Setup Files
  setupFilesAfterEnv: ['<rootDir>/tests/setup.js'],
  
  // Test Timeout
  testTimeout: 30000,
  
  // Verbose Output
  verbose: true,
  
  // Clear Mocks
  clearMocks: true,
  restoreMocks: true,
  
  // Transform - disable for ES modules
  transform: {}
};
