/**
 * Scraping Service
 * 
 * Handles all scraping operations including:
 * - Event scraping
 * - Batch scraping operations
 * - Scraping job management
 * - Error handling and retries
 */

import { DSVScraper } from '../../scrapers/DSVScraper.js';
import { backendLogger } from '../../utils/logger.js';
import config from '../../config.js';

export default class ScrapingService {
    constructor(database) {
        this.database = database;
        this.scraper = null;
        this.isInitialized = false;
        this.activeScrapeJobs = new Map();
        this.scrapeHistory = [];
        this.maxHistorySize = 100;
    }
    
    /**
     * Initialize the scraping service
     */
    async initialize() {
        try {
            backendLogger.info('🔧 Initializing scraping service...');
            
            // Initialize DSV scraper
            this.scraper = new DSVScraper('src/scrapers/config/events.json');
            await this.scraper.loadConfig();
            
            this.isInitialized = true;
            backendLogger.info('✅ Scraping service initialized');
            
        } catch (error) {
            backendLogger.error('❌ Failed to initialize scraping service', {
                error: error.message
            });
            throw error;
        }
    }
    
    /**
     * Scrape a single event
     */
    async scrapeEvent(eventConfig) {
        const jobId = this._generateJobId('event');
        
        try {
            backendLogger.info('🕷️ Starting event scraping', {
                jobId,
                eventConfig
            });
            
            // Track active job
            this.activeScrapeJobs.set(jobId, {
                type: 'event',
                config: eventConfig,
                startTime: new Date(),
                status: 'running'
            });
            
            // Perform scraping
            const results = await this.scraper.scrapeEvent(eventConfig);
            
            // Update job status
            const job = this.activeScrapeJobs.get(jobId);
            job.status = 'completed';
            job.endTime = new Date();
            job.results = {
                rankingsFound: results.length,
                success: true
            };
            
            // Move to history
            this._moveJobToHistory(jobId);
            
            backendLogger.info('✅ Event scraping completed', {
                jobId,
                rankingsFound: results.length
            });
            
            return {
                jobId,
                success: true,
                rankingsFound: results.length,
                results
            };
            
        } catch (error) {
            // Update job status with error
            const job = this.activeScrapeJobs.get(jobId);
            if (job) {
                job.status = 'failed';
                job.endTime = new Date();
                job.error = error.message;
                this._moveJobToHistory(jobId);
            }
            
            backendLogger.error('❌ Event scraping failed', {
                jobId,
                error: error.message,
                eventConfig
            });
            
            throw error;
        }
    }
    
    /**
     * Scrape all configured events
     */
    async scrapeAllEvents() {
        const jobId = this._generateJobId('all');
        
        try {
            backendLogger.info('🕷️ Starting full scraping operation', { jobId });
            
            // Track active job
            this.activeScrapeJobs.set(jobId, {
                type: 'all_events',
                startTime: new Date(),
                status: 'running'
            });
            
            // Perform scraping
            const results = await this.scraper.scrapeAllEvents();
            
            // Calculate statistics
            const stats = this._calculateScrapeStats(results);
            
            // Update job status
            const job = this.activeScrapeJobs.get(jobId);
            job.status = 'completed';
            job.endTime = new Date();
            job.results = stats;
            
            // Move to history
            this._moveJobToHistory(jobId);
            
            backendLogger.info('✅ Full scraping operation completed', {
                jobId,
                stats
            });
            
            return {
                jobId,
                success: true,
                stats,
                results
            };
            
        } catch (error) {
            // Update job status with error
            const job = this.activeScrapeJobs.get(jobId);
            if (job) {
                job.status = 'failed';
                job.endTime = new Date();
                job.error = error.message;
                this._moveJobToHistory(jobId);
            }
            
            backendLogger.error('❌ Full scraping operation failed', {
                jobId,
                error: error.message
            });
            
            throw error;
        }
    }
    
    /**
     * Get status of a scraping job
     */
    getJobStatus(jobId) {
        // Check active jobs first
        if (this.activeScrapeJobs.has(jobId)) {
            return this.activeScrapeJobs.get(jobId);
        }
        
        // Check history
        const historyJob = this.scrapeHistory.find(job => job.jobId === jobId);
        return historyJob || null;
    }
    
    /**
     * Get all active scraping jobs
     */
    getActiveJobs() {
        return Array.from(this.activeScrapeJobs.entries()).map(([jobId, job]) => ({
            jobId,
            ...job
        }));
    }
    
    /**
     * Get scraping history
     */
    getScrapeHistory(limit = 20) {
        return this.scrapeHistory.slice(0, limit);
    }
    
    /**
     * Cancel a running scraping job
     */
    async cancelJob(jobId) {
        if (!this.activeScrapeJobs.has(jobId)) {
            throw new Error(`Job ${jobId} not found or not active`);
        }
        
        const job = this.activeScrapeJobs.get(jobId);
        job.status = 'cancelled';
        job.endTime = new Date();
        
        this._moveJobToHistory(jobId);
        
        backendLogger.info('🚫 Scraping job cancelled', { jobId });
        
        return { success: true, jobId };
    }
    
    /**
     * Health check for scraping service
     */
    async healthCheck() {
        try {
            const status = {
                status: 'healthy',
                isInitialized: this.isInitialized,
                activeJobs: this.activeScrapeJobs.size,
                historySize: this.scrapeHistory.length,
                scraperConfig: this.scraper ? 'loaded' : 'not_loaded'
            };
            
            // Check if scraper is properly configured
            if (!this.scraper || !this.isInitialized) {
                status.status = 'warning';
                status.message = 'Scraper not properly initialized';
            }
            
            return status;
            
        } catch (error) {
            return {
                status: 'error',
                error: error.message
            };
        }
    }
    
    /**
     * Generate unique job ID
     */
    _generateJobId(type) {
        const timestamp = Date.now();
        const random = Math.random().toString(36).substr(2, 9);
        return `${type}_${timestamp}_${random}`;
    }
    
    /**
     * Move job from active to history
     */
    _moveJobToHistory(jobId) {
        const job = this.activeScrapeJobs.get(jobId);
        if (job) {
            this.scrapeHistory.unshift({
                jobId,
                ...job
            });
            
            // Limit history size
            if (this.scrapeHistory.length > this.maxHistorySize) {
                this.scrapeHistory = this.scrapeHistory.slice(0, this.maxHistorySize);
            }
            
            this.activeScrapeJobs.delete(jobId);
        }
    }
    
    /**
     * Calculate statistics from scraping results
     */
    _calculateScrapeStats(results) {
        const stats = {
            totalEvents: 0,
            totalRankings: 0,
            successfulEvents: 0,
            failedEvents: 0
        };
        
        for (const [eventKey, rankings] of Object.entries(results)) {
            stats.totalEvents++;
            if (Array.isArray(rankings) && rankings.length > 0) {
                stats.successfulEvents++;
                stats.totalRankings += rankings.length;
            } else {
                stats.failedEvents++;
            }
        }
        
        return stats;
    }
}
