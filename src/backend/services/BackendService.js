/**
 * Main Backend Service Class
 * 
 * Orchestrates all backend operations including:
 * - Database management
 * - Scraping operations
 * - Data processing
 * - Job scheduling
 * - Health monitoring
 */

import config from '../../config.js';
import { backendLogger } from '../../utils/logger.js';
import DatabaseManager from '../../utils/DatabaseManager.js';
import ScrapingService from './ScrapingService.js';
import DataProcessingService from './DataProcessingService.js';
import JobSchedulerService from './JobSchedulerService.js';

export default class BackendService {
    constructor() {
        this.isInitialized = false;
        this.isRunning = false;
        
        // Core services
        this.database = null;
        this.scrapingService = null;
        this.dataProcessingService = null;
        this.jobSchedulerService = null;
        
        // Service status
        this.startTime = null;
        this.lastHealthCheck = null;
    }
    
    /**
     * Initialize all backend services
     */
    async initialize() {
        try {
            backendLogger.info('🔧 Initializing backend services...');
            
            // Initialize database connection
            this.database = new DatabaseManager();
            await this.database.connect();
            backendLogger.info('✅ Database service initialized');
            
            // Initialize scraping service
            this.scrapingService = new ScrapingService(this.database);
            await this.scrapingService.initialize();
            backendLogger.info('✅ Scraping service initialized');
            
            // Initialize data processing service
            this.dataProcessingService = new DataProcessingService(this.database);
            await this.dataProcessingService.initialize();
            backendLogger.info('✅ Data processing service initialized');
            
            // Initialize job scheduler service
            this.jobSchedulerService = new JobSchedulerService({
                database: this.database,
                scrapingService: this.scrapingService,
                dataProcessingService: this.dataProcessingService
            });
            await this.jobSchedulerService.initialize();
            backendLogger.info('✅ Job scheduler service initialized');
            
            this.isInitialized = true;
            backendLogger.info('✅ All backend services initialized successfully');
            
        } catch (error) {
            backendLogger.error('❌ Failed to initialize backend services', {
                error: error.message,
                stack: error.stack
            });
            throw error;
        }
    }
    
    /**
     * Start all backend services
     */
    async start() {
        try {
            if (!this.isInitialized) {
                throw new Error('Backend service must be initialized before starting');
            }
            
            backendLogger.info('🚀 Starting backend services...');
            
            // Start job scheduler (if enabled)
            if (config.ENABLE_SCHEDULER) {
                await this.jobSchedulerService.start();
                backendLogger.info('✅ Job scheduler started');
            }
            
            this.isRunning = true;
            this.startTime = new Date();
            
            backendLogger.info('✅ All backend services started successfully');
            
        } catch (error) {
            backendLogger.error('❌ Failed to start backend services', {
                error: error.message,
                stack: error.stack
            });
            throw error;
        }
    }
    
    /**
     * Stop all backend services gracefully
     */
    async stop() {
        try {
            backendLogger.info('📴 Stopping backend services...');
            
            // Stop job scheduler
            if (this.jobSchedulerService) {
                await this.jobSchedulerService.stop();
                backendLogger.info('✅ Job scheduler stopped');
            }
            
            // Close database connection
            if (this.database) {
                await this.database.disconnect();
                backendLogger.info('✅ Database connection closed');
            }
            
            this.isRunning = false;
            backendLogger.info('✅ All backend services stopped successfully');
            
        } catch (error) {
            backendLogger.error('❌ Error stopping backend services', {
                error: error.message
            });
            throw error;
        }
    }
    
    /**
     * Perform health check on all services
     */
    async healthCheck() {
        try {
            const healthStatus = {
                status: 'healthy',
                timestamp: new Date().toISOString(),
                uptime: this.startTime ? Date.now() - this.startTime.getTime() : 0,
                services: {}
            };
            
            // Check database health
            if (this.database) {
                healthStatus.services.database = await this.database.healthCheck();
            } else {
                healthStatus.services.database = { status: 'not_initialized' };
            }
            
            // Check scraping service health
            if (this.scrapingService) {
                healthStatus.services.scraping = await this.scrapingService.healthCheck();
            } else {
                healthStatus.services.scraping = { status: 'not_initialized' };
            }
            
            // Check data processing service health
            if (this.dataProcessingService) {
                healthStatus.services.dataProcessing = await this.dataProcessingService.healthCheck();
            } else {
                healthStatus.services.dataProcessing = { status: 'not_initialized' };
            }
            
            // Check job scheduler health
            if (this.jobSchedulerService) {
                healthStatus.services.jobScheduler = await this.jobSchedulerService.healthCheck();
            } else {
                healthStatus.services.jobScheduler = { status: 'not_initialized' };
            }
            
            // Determine overall status
            const serviceStatuses = Object.values(healthStatus.services).map(s => s.status);
            if (serviceStatuses.some(status => status === 'error' || status === 'unhealthy')) {
                healthStatus.status = 'unhealthy';
            } else if (serviceStatuses.some(status => status === 'warning')) {
                healthStatus.status = 'warning';
            }
            
            this.lastHealthCheck = new Date();
            return healthStatus;
            
        } catch (error) {
            backendLogger.error('❌ Health check failed', {
                error: error.message
            });
            
            return {
                status: 'error',
                timestamp: new Date().toISOString(),
                error: error.message
            };
        }
    }
    
    /**
     * Get service statistics
     */
    getStats() {
        return {
            isInitialized: this.isInitialized,
            isRunning: this.isRunning,
            startTime: this.startTime,
            lastHealthCheck: this.lastHealthCheck,
            uptime: this.startTime ? Date.now() - this.startTime.getTime() : 0
        };
    }
    
    /**
     * Get database service
     */
    getDatabase() {
        return this.database;
    }
    
    /**
     * Get scraping service
     */
    getScrapingService() {
        return this.scrapingService;
    }
    
    /**
     * Get data processing service
     */
    getDataProcessingService() {
        return this.dataProcessingService;
    }
    
    /**
     * Get job scheduler service
     */
    getJobSchedulerService() {
        return this.jobSchedulerService;
    }
}
