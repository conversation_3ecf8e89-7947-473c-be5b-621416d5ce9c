/**
 * Job Scheduler Service
 * 
 * Handles scheduled operations including:
 * - Cron job management
 * - Scheduled scraping operations
 * - Data cleanup jobs
 * - Health monitoring jobs
 */

import cron from 'node-cron';
import { backendLogger } from '../../utils/logger.js';
import config from '../../config.js';

export default class JobSchedulerService {
    constructor({ database, scrapingService, dataProcessingService }) {
        this.database = database;
        this.scrapingService = scrapingService;
        this.dataProcessingService = dataProcessingService;
        
        this.isInitialized = false;
        this.isRunning = false;
        this.scheduledJobs = new Map();
        this.jobHistory = [];
        this.maxHistorySize = 100;
    }
    
    /**
     * Initialize the job scheduler service
     */
    async initialize() {
        try {
            backendLogger.info('🔧 Initializing job scheduler service...');
            
            // Setup default scheduled jobs
            this._setupDefaultJobs();
            
            this.isInitialized = true;
            backendLogger.info('✅ Job scheduler service initialized');
            
        } catch (error) {
            backendLogger.error('❌ Failed to initialize job scheduler service', {
                error: error.message
            });
            throw error;
        }
    }
    
    /**
     * Start the job scheduler
     */
    async start() {
        try {
            if (!this.isInitialized) {
                throw new Error('Job scheduler must be initialized before starting');
            }
            
            backendLogger.info('🚀 Starting job scheduler...');
            
            // Start all scheduled jobs
            for (const [jobName, jobConfig] of this.scheduledJobs) {
                if (jobConfig.enabled) {
                    jobConfig.task.start();
                    backendLogger.info(`✅ Started scheduled job: ${jobName}`);
                }
            }
            
            this.isRunning = true;
            backendLogger.info('✅ Job scheduler started successfully');
            
        } catch (error) {
            backendLogger.error('❌ Failed to start job scheduler', {
                error: error.message
            });
            throw error;
        }
    }
    
    /**
     * Stop the job scheduler
     */
    async stop() {
        try {
            backendLogger.info('📴 Stopping job scheduler...');
            
            // Stop all scheduled jobs
            for (const [jobName, jobConfig] of this.scheduledJobs) {
                if (jobConfig.task) {
                    jobConfig.task.stop();
                    backendLogger.info(`✅ Stopped scheduled job: ${jobName}`);
                }
            }
            
            this.isRunning = false;
            backendLogger.info('✅ Job scheduler stopped successfully');
            
        } catch (error) {
            backendLogger.error('❌ Error stopping job scheduler', {
                error: error.message
            });
            throw error;
        }
    }
    
    /**
     * Add a new scheduled job
     */
    addJob(jobName, cronExpression, jobFunction, options = {}) {
        try {
            const task = cron.schedule(cronExpression, async () => {
                await this._executeJob(jobName, jobFunction);
            }, {
                scheduled: false,
                timezone: options.timezone || 'Europe/Berlin'
            });
            
            this.scheduledJobs.set(jobName, {
                cronExpression,
                task,
                enabled: options.enabled !== false,
                description: options.description || '',
                lastRun: null,
                nextRun: null,
                runCount: 0
            });
            
            backendLogger.info('✅ Added scheduled job', {
                jobName,
                cronExpression,
                enabled: options.enabled !== false
            });
            
            return true;
            
        } catch (error) {
            backendLogger.error('❌ Failed to add scheduled job', {
                jobName,
                error: error.message
            });
            throw error;
        }
    }
    
    /**
     * Remove a scheduled job
     */
    removeJob(jobName) {
        try {
            const jobConfig = this.scheduledJobs.get(jobName);
            if (jobConfig) {
                jobConfig.task.stop();
                this.scheduledJobs.delete(jobName);
                
                backendLogger.info('✅ Removed scheduled job', { jobName });
                return true;
            }
            
            return false;
            
        } catch (error) {
            backendLogger.error('❌ Failed to remove scheduled job', {
                jobName,
                error: error.message
            });
            throw error;
        }
    }
    
    /**
     * Enable/disable a scheduled job
     */
    toggleJob(jobName, enabled) {
        try {
            const jobConfig = this.scheduledJobs.get(jobName);
            if (jobConfig) {
                if (enabled && !jobConfig.enabled) {
                    jobConfig.task.start();
                    jobConfig.enabled = true;
                } else if (!enabled && jobConfig.enabled) {
                    jobConfig.task.stop();
                    jobConfig.enabled = false;
                }
                
                backendLogger.info('✅ Toggled scheduled job', {
                    jobName,
                    enabled
                });
                
                return true;
            }
            
            return false;
            
        } catch (error) {
            backendLogger.error('❌ Failed to toggle scheduled job', {
                jobName,
                error: error.message
            });
            throw error;
        }
    }
    
    /**
     * Get all scheduled jobs
     */
    getScheduledJobs() {
        const jobs = [];
        
        for (const [jobName, jobConfig] of this.scheduledJobs) {
            jobs.push({
                name: jobName,
                cronExpression: jobConfig.cronExpression,
                enabled: jobConfig.enabled,
                description: jobConfig.description,
                lastRun: jobConfig.lastRun,
                nextRun: jobConfig.nextRun,
                runCount: jobConfig.runCount
            });
        }
        
        return jobs;
    }
    
    /**
     * Get job execution history
     */
    getJobHistory(limit = 20) {
        return this.jobHistory.slice(0, limit);
    }
    
    /**
     * Execute a job manually
     */
    async executeJobManually(jobName) {
        const jobConfig = this.scheduledJobs.get(jobName);
        if (!jobConfig) {
            throw new Error(`Job ${jobName} not found`);
        }
        
        // Execute the job function manually
        return await this._executeJob(jobName, jobConfig.jobFunction, true);
    }
    
    /**
     * Health check for job scheduler service
     */
    async healthCheck() {
        try {
            const status = {
                status: 'healthy',
                isInitialized: this.isInitialized,
                isRunning: this.isRunning,
                scheduledJobsCount: this.scheduledJobs.size,
                activeJobs: 0,
                historySize: this.jobHistory.length
            };
            
            // Count active jobs
            for (const [, jobConfig] of this.scheduledJobs) {
                if (jobConfig.enabled) {
                    status.activeJobs++;
                }
            }
            
            return status;
            
        } catch (error) {
            return {
                status: 'error',
                error: error.message
            };
        }
    }
    
    /**
     * Setup default scheduled jobs
     */
    _setupDefaultJobs() {
        // Weekly scraping job
        this.addJob(
            'weekly_scraping',
            '0 2 * * 1', // Every Monday at 2 AM
            async () => {
                const scrapingResult = await this.scrapingService.scrapeAllEvents();
                const processingResult = await this.dataProcessingService.processScrapingResults(scrapingResult.results);
                return { scrapingResult, processingResult };
            },
            {
                description: 'Weekly full scraping of all events',
                enabled: config.ENABLE_WEEKLY_SCRAPING || false
            }
        );
        
        // Daily cleanup job
        this.addJob(
            'daily_cleanup',
            '0 1 * * *', // Every day at 1 AM
            async () => {
                return await this.dataProcessingService.cleanOldData({
                    olderThanDays: 365
                });
            },
            {
                description: 'Daily cleanup of old data',
                enabled: config.ENABLE_DAILY_CLEANUP || false
            }
        );
        
        // Health check job
        this.addJob(
            'health_check',
            '*/15 * * * *', // Every 15 minutes
            async () => {
                const dbHealth = await this.database.healthCheck();
                const scrapingHealth = await this.scrapingService.healthCheck();
                const processingHealth = await this.dataProcessingService.healthCheck();
                
                return {
                    database: dbHealth,
                    scraping: scrapingHealth,
                    processing: processingHealth
                };
            },
            {
                description: 'Regular health check of all services',
                enabled: config.ENABLE_HEALTH_CHECKS || false
            }
        );
    }
    
    /**
     * Execute a job with error handling and logging
     */
    async _executeJob(jobName, jobFunction, isManual = false) {
        const startTime = new Date();
        const executionId = `${jobName}_${Date.now()}`;
        
        try {
            backendLogger.info('🔄 Executing scheduled job', {
                jobName,
                executionId,
                isManual
            });
            
            // Execute the job function
            const result = await jobFunction();
            
            const endTime = new Date();
            const duration = endTime.getTime() - startTime.getTime();
            
            // Update job config
            const jobConfig = this.scheduledJobs.get(jobName);
            if (jobConfig) {
                jobConfig.lastRun = endTime;
                jobConfig.runCount++;
            }
            
            // Add to history
            this._addToHistory({
                executionId,
                jobName,
                startTime,
                endTime,
                duration,
                status: 'success',
                result,
                isManual
            });
            
            backendLogger.info('✅ Scheduled job completed successfully', {
                jobName,
                executionId,
                duration: `${duration}ms`
            });
            
            return { success: true, result, duration };
            
        } catch (error) {
            const endTime = new Date();
            const duration = endTime.getTime() - startTime.getTime();
            
            // Add to history
            this._addToHistory({
                executionId,
                jobName,
                startTime,
                endTime,
                duration,
                status: 'failed',
                error: error.message,
                isManual
            });
            
            backendLogger.error('❌ Scheduled job failed', {
                jobName,
                executionId,
                error: error.message,
                duration: `${duration}ms`
            });
            
            throw error;
        }
    }
    
    /**
     * Add job execution to history
     */
    _addToHistory(execution) {
        this.jobHistory.unshift(execution);
        
        // Limit history size
        if (this.jobHistory.length > this.maxHistorySize) {
            this.jobHistory = this.jobHistory.slice(0, this.maxHistorySize);
        }
    }
}
