/**
 * Data Processing Service
 * 
 * Handles all data processing operations including:
 * - Processing scraped data
 * - Data validation and cleaning
 * - Database operations
 * - Data transformation
 */

import { DataProcessor } from '../../utils/DataProcessor.js';
import { backendLogger } from '../../utils/logger.js';

export default class DataProcessingService {
    constructor(database) {
        this.database = database;
        this.dataProcessor = null;
        this.isInitialized = false;
        this.activeProcessingJobs = new Map();
        this.processingHistory = [];
        this.maxHistorySize = 100;
    }
    
    /**
     * Initialize the data processing service
     */
    async initialize() {
        try {
            backendLogger.info('🔧 Initializing data processing service...');
            
            // Initialize data processor
            this.dataProcessor = new DataProcessor(this.database);
            
            this.isInitialized = true;
            backendLogger.info('✅ Data processing service initialized');
            
        } catch (error) {
            backendLogger.error('❌ Failed to initialize data processing service', {
                error: error.message
            });
            throw error;
        }
    }
    
    /**
     * Process scraping results
     */
    async processScrapingResults(scrapingResults) {
        const jobId = this._generateJobId('process');
        
        try {
            backendLogger.info('⚙️ Starting data processing', {
                jobId,
                eventsCount: Object.keys(scrapingResults).length
            });
            
            // Track active job
            this.activeProcessingJobs.set(jobId, {
                type: 'scraping_results',
                startTime: new Date(),
                status: 'running',
                eventsCount: Object.keys(scrapingResults).length
            });
            
            // Process the data
            const results = await this.dataProcessor.processScrapingResults(scrapingResults);
            
            // Calculate statistics
            const stats = this._calculateProcessingStats(results);
            
            // Update job status
            const job = this.activeProcessingJobs.get(jobId);
            job.status = 'completed';
            job.endTime = new Date();
            job.results = stats;
            
            // Move to history
            this._moveJobToHistory(jobId);
            
            backendLogger.info('✅ Data processing completed', {
                jobId,
                stats
            });
            
            return {
                jobId,
                success: true,
                stats,
                results
            };
            
        } catch (error) {
            // Update job status with error
            const job = this.activeProcessingJobs.get(jobId);
            if (job) {
                job.status = 'failed';
                job.endTime = new Date();
                job.error = error.message;
                this._moveJobToHistory(jobId);
            }
            
            backendLogger.error('❌ Data processing failed', {
                jobId,
                error: error.message
            });
            
            throw error;
        }
    }
    
    /**
     * Process single event data
     */
    async processEventData(eventKey, rankings) {
        const jobId = this._generateJobId('event');
        
        try {
            backendLogger.info('⚙️ Processing event data', {
                jobId,
                eventKey,
                rankingsCount: rankings.length
            });
            
            // Track active job
            this.activeProcessingJobs.set(jobId, {
                type: 'event_data',
                eventKey,
                startTime: new Date(),
                status: 'running',
                rankingsCount: rankings.length
            });
            
            // Process single event
            const result = await this.dataProcessor.processEventData(eventKey, rankings);
            
            // Update job status
            const job = this.activeProcessingJobs.get(jobId);
            job.status = 'completed';
            job.endTime = new Date();
            job.results = result;
            
            // Move to history
            this._moveJobToHistory(jobId);
            
            backendLogger.info('✅ Event data processing completed', {
                jobId,
                saved: result.saved,
                updated: result.updated
            });
            
            return {
                jobId,
                success: true,
                result
            };
            
        } catch (error) {
            // Update job status with error
            const job = this.activeProcessingJobs.get(jobId);
            if (job) {
                job.status = 'failed';
                job.endTime = new Date();
                job.error = error.message;
                this._moveJobToHistory(jobId);
            }
            
            backendLogger.error('❌ Event data processing failed', {
                jobId,
                error: error.message,
                eventKey
            });
            
            throw error;
        }
    }
    
    /**
     * Clean old data
     */
    async cleanOldData(options = {}) {
        const jobId = this._generateJobId('cleanup');
        
        try {
            backendLogger.info('🧹 Starting data cleanup', { jobId, options });
            
            // Track active job
            this.activeProcessingJobs.set(jobId, {
                type: 'cleanup',
                startTime: new Date(),
                status: 'running',
                options
            });
            
            // Perform cleanup
            const result = await this.dataProcessor.cleanOldData(options);
            
            // Update job status
            const job = this.activeProcessingJobs.get(jobId);
            job.status = 'completed';
            job.endTime = new Date();
            job.results = result;
            
            // Move to history
            this._moveJobToHistory(jobId);
            
            backendLogger.info('✅ Data cleanup completed', {
                jobId,
                deletedRecords: result.deletedRecords
            });
            
            return {
                jobId,
                success: true,
                result
            };
            
        } catch (error) {
            // Update job status with error
            const job = this.activeProcessingJobs.get(jobId);
            if (job) {
                job.status = 'failed';
                job.endTime = new Date();
                job.error = error.message;
                this._moveJobToHistory(jobId);
            }
            
            backendLogger.error('❌ Data cleanup failed', {
                jobId,
                error: error.message
            });
            
            throw error;
        }
    }
    
    /**
     * Get status of a processing job
     */
    getJobStatus(jobId) {
        // Check active jobs first
        if (this.activeProcessingJobs.has(jobId)) {
            return this.activeProcessingJobs.get(jobId);
        }
        
        // Check history
        const historyJob = this.processingHistory.find(job => job.jobId === jobId);
        return historyJob || null;
    }
    
    /**
     * Get all active processing jobs
     */
    getActiveJobs() {
        return Array.from(this.activeProcessingJobs.entries()).map(([jobId, job]) => ({
            jobId,
            ...job
        }));
    }
    
    /**
     * Get processing history
     */
    getProcessingHistory(limit = 20) {
        return this.processingHistory.slice(0, limit);
    }
    
    /**
     * Health check for data processing service
     */
    async healthCheck() {
        try {
            const status = {
                status: 'healthy',
                isInitialized: this.isInitialized,
                activeJobs: this.activeProcessingJobs.size,
                historySize: this.processingHistory.length,
                dataProcessor: this.dataProcessor ? 'loaded' : 'not_loaded'
            };
            
            // Check if data processor is properly configured
            if (!this.dataProcessor || !this.isInitialized) {
                status.status = 'warning';
                status.message = 'Data processor not properly initialized';
            }
            
            return status;
            
        } catch (error) {
            return {
                status: 'error',
                error: error.message
            };
        }
    }
    
    /**
     * Generate unique job ID
     */
    _generateJobId(type) {
        const timestamp = Date.now();
        const random = Math.random().toString(36).substr(2, 9);
        return `${type}_${timestamp}_${random}`;
    }
    
    /**
     * Move job from active to history
     */
    _moveJobToHistory(jobId) {
        const job = this.activeProcessingJobs.get(jobId);
        if (job) {
            this.processingHistory.unshift({
                jobId,
                ...job
            });
            
            // Limit history size
            if (this.processingHistory.length > this.maxHistorySize) {
                this.processingHistory = this.processingHistory.slice(0, this.maxHistorySize);
            }
            
            this.activeProcessingJobs.delete(jobId);
        }
    }
    
    /**
     * Calculate statistics from processing results
     */
    _calculateProcessingStats(results) {
        const stats = {
            totalEvents: 0,
            totalSaved: 0,
            totalUpdated: 0,
            totalErrors: 0
        };
        
        for (const [eventKey, result] of Object.entries(results)) {
            stats.totalEvents++;
            if (result.saved) stats.totalSaved += result.saved;
            if (result.updated) stats.totalUpdated += result.updated;
            if (result.errors) stats.totalErrors += result.errors;
        }
        
        return stats;
    }
}
