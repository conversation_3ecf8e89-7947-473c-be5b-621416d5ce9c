/**
 * Backend Service Entry Point for DSV Scraper
 * 
 * This module provides a standalone backend service that handles:
 * - Data scraping operations
 * - Database management
 * - Data processing
 * - Job scheduling
 * 
 * The backend service is designed to run independently and can be
 * accessed through the API layer or used directly by other services.
 */

import config from '../config.js';
import { backendLogger } from '../utils/logger.js';
import BackendService from './services/BackendService.js';

/**
 * Main backend service instance
 */
let backendService = null;

/**
 * Initialize and start the backend service
 */
export async function startBackendService() {
    try {
        backendLogger.info('🚀 Starting DSV Backend Service');
        
        // Create backend service instance
        backendService = new BackendService();
        
        // Initialize the service
        await backendService.initialize();
        
        // Start background services
        await backendService.start();
        
        backendLogger.info('✅ DSV Backend Service started successfully');
        
        return backendService;
        
    } catch (error) {
        backendLogger.error('❌ Failed to start backend service', {
            error: error.message,
            stack: error.stack
        });
        throw error;
    }
}

/**
 * Stop the backend service gracefully
 */
export async function stopBackendService() {
    try {
        if (backendService) {
            backendLogger.info('📴 Stopping DSV Backend Service');
            await backendService.stop();
            backendService = null;
            backendLogger.info('✅ DSV Backend Service stopped successfully');
        }
    } catch (error) {
        backendLogger.error('❌ Error stopping backend service', {
            error: error.message
        });
        throw error;
    }
}

/**
 * Get the current backend service instance
 */
export function getBackendService() {
    return backendService;
}

/**
 * Health check for the backend service
 */
export async function healthCheck() {
    try {
        if (!backendService) {
            return {
                status: 'stopped',
                message: 'Backend service is not running'
            };
        }
        
        return await backendService.healthCheck();
        
    } catch (error) {
        return {
            status: 'error',
            message: error.message
        };
    }
}

/**
 * Standalone backend service runner
 */
async function runStandaloneBackend() {
    try {
        // Start the backend service
        await startBackendService();
        
        // Setup graceful shutdown
        const gracefulShutdown = async (signal) => {
            backendLogger.info(`📴 ${signal} received. Starting graceful shutdown...`);
            await stopBackendService();
            process.exit(0);
        };
        
        // Signal handlers
        process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
        process.on('SIGINT', () => gracefulShutdown('SIGINT'));
        
        // Unhandled promise rejections
        process.on('unhandledRejection', (reason, promise) => {
            backendLogger.error('❌ Unhandled Promise Rejection', {
                reason: reason?.message || reason,
                stack: reason?.stack,
                promise
            });
        });
        
        // Uncaught exceptions
        process.on('uncaughtException', (error) => {
            backendLogger.error('❌ Uncaught Exception', {
                error: error.message,
                stack: error.stack
            });
            process.exit(1);
        });
        
        backendLogger.info('✅ Backend service is running. Press Ctrl+C to stop.');
        
    } catch (error) {
        backendLogger.error('❌ Failed to start standalone backend', {
            error: error.message,
            stack: error.stack
        });
        process.exit(1);
    }
}

// Check if this script is being run directly
if (import.meta.url === `file://${process.argv[1]}`) {
    runStandaloneBackend();
}

export default {
    startBackendService,
    stopBackendService,
    getBackendService,
    healthCheck
};
