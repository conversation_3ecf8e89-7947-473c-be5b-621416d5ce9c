/**
 * Frontend Service Entry Point
 * 
 * This module provides a standalone frontend service that serves the web interface
 * and communicates exclusively with the backend through API endpoints.
 * 
 * Features:
 * - Serves static web interface
 * - Proxies API requests to backend
 * - Handles authentication and session management
 * - Provides client-side routing
 */

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import path from 'path';
import { fileURLToPath } from 'url';
import { createProxyMiddleware } from 'http-proxy-middleware';

import config from '../config.js';
import { frontendLogger } from '../utils/logger.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Create and configure the frontend server
 */
export function createFrontendServer() {
    const app = express();
    
    // Trust proxy for correct IP addresses
    app.set('trust proxy', 1);
    
    // View engine setup
    app.set('view engine', 'ejs');
    app.set('views', path.join(__dirname, 'views'));
    
    // Security middleware
    app.use(helmet({
        contentSecurityPolicy: {
            directives: {
                defaultSrc: ["'self'"],
                styleSrc: ["'self'", "'unsafe-inline'", "https://cdn.jsdelivr.net"],
                scriptSrc: ["'self'", "'unsafe-inline'", "https://cdn.jsdelivr.net"],
                imgSrc: ["'self'", "data:", "https:"],
                fontSrc: ["'self'", "https://cdn.jsdelivr.net"],
                connectSrc: ["'self'", `http://localhost:${config.API_PORT || 3001}`]
            }
        }
    }));
    
    // CORS configuration
    app.use(cors({
        origin: config.CORS_ORIGINS,
        credentials: true,
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
        allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
    }));
    
    // Compression
    app.use(compression());
    
    // Static files
    app.use('/static', express.static(path.join(__dirname, 'public'), {
        maxAge: config.NODE_ENV === 'production' ? '1d' : '0',
        etag: true
    }));
    
    // Request logging middleware
    app.use((req, res, next) => {
        const startTime = Date.now();
        
        res.on('finish', () => {
            const duration = Date.now() - startTime;
            frontendLogger.info('Frontend Request', {
                method: req.method,
                url: req.url,
                status: res.statusCode,
                duration: `${duration}ms`,
                ip: req.ip,
                userAgent: req.get('User-Agent')
            });
        });
        
        next();
    });
    
    // API proxy middleware - forwards all /api requests to the backend API server
    const apiPort = config.API_PORT || 3001;
    const apiProxy = createProxyMiddleware({
        target: `http://localhost:${apiPort}`,
        changeOrigin: true,
        pathRewrite: {
            '^/api': '/api' // Keep the /api prefix
        },
        onError: (err, req, res) => {
            frontendLogger.error('API proxy error', {
                error: err.message,
                url: req.url
            });
            res.status(503).json({
                error: 'API service unavailable',
                message: 'Unable to connect to backend API'
            });
        },
        onProxyReq: (proxyReq, req, res) => {
            frontendLogger.debug('Proxying API request', {
                method: req.method,
                url: req.url,
                target: `http://localhost:${apiPort}${req.url}`
            });
        }
    });
    
    app.use('/api', apiProxy);
    
    // Frontend routes
    app.use('/', createFrontendRoutes());
    
    // Health check endpoint
    app.get('/health', (req, res) => {
        res.json({
            status: 'healthy',
            service: 'frontend',
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            memory: process.memoryUsage(),
            apiEndpoint: `http://localhost:${apiPort}`
        });
    });
    
    // 404 handler
    app.use((req, res) => {
        res.status(404).render('error', {
            title: 'Page Not Found',
            error: {
                status: 404,
                message: 'The requested page was not found.'
            }
        });
    });
    
    // Error handler
    app.use((error, req, res, next) => {
        frontendLogger.error('Unhandled frontend error', {
            error: error.message,
            stack: error.stack,
            url: req.url,
            method: req.method,
            ip: req.ip
        });
        
        const status = error.status || 500;
        const message = config.NODE_ENV === 'development' ? error.message : 'Internal server error';
        
        res.status(status).render('error', {
            title: 'Error',
            error: {
                status,
                message
            }
        });
    });
    
    return app;
}

/**
 * Create frontend routes
 */
function createFrontendRoutes() {
    const router = express.Router();
    
    // Home page
    router.get('/', (req, res) => {
        res.render('index', {
            title: 'DSV Scraper Dashboard',
            apiEndpoint: `/api`
        });
    });
    
    // Rankings page
    router.get('/rankings', (req, res) => {
        res.render('rankings', {
            title: 'Rankings - DSV Scraper',
            apiEndpoint: `/api`
        });
    });
    
    // Events page
    router.get('/events', (req, res) => {
        res.render('events', {
            title: 'Events - DSV Scraper',
            apiEndpoint: `/api`
        });
    });
    
    // Jobs page
    router.get('/jobs', (req, res) => {
        res.render('jobs', {
            title: 'Jobs - DSV Scraper',
            apiEndpoint: `/api`
        });
    });
    
    // System status page
    router.get('/system', (req, res) => {
        res.render('system', {
            title: 'System Status - DSV Scraper',
            apiEndpoint: `/api`
        });
    });
    
    return router;
}

/**
 * Start the frontend server
 */
export async function startFrontendServer() {
    try {
        frontendLogger.info('🚀 Starting DSV Frontend Server');
        
        const app = createFrontendServer();
        const port = config.FRONTEND_PORT || config.PORT || 3000;
        
        const server = app.listen(port, () => {
            frontendLogger.info('✅ DSV Frontend Server started', {
                port,
                environment: config.NODE_ENV,
                pid: process.pid,
                apiEndpoint: `http://localhost:${config.API_PORT || 3001}`
            });
        });
        
        // Graceful shutdown
        const gracefulShutdown = async (signal) => {
            frontendLogger.info(`📴 ${signal} received. Starting graceful shutdown...`);
            
            server.close(() => {
                frontendLogger.info('🔒 Frontend server closed');
                frontendLogger.info('✅ Graceful shutdown completed');
                process.exit(0);
            });
            
            // Force shutdown after 30 seconds
            setTimeout(() => {
                frontendLogger.error('⚠️ Force shutdown after timeout');
                process.exit(1);
            }, 30000);
        };
        
        // Signal handlers
        process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
        process.on('SIGINT', () => gracefulShutdown('SIGINT'));
        
        // Unhandled promise rejections
        process.on('unhandledRejection', (reason, promise) => {
            frontendLogger.error('❌ Unhandled Promise Rejection', {
                reason: reason?.message || reason,
                stack: reason?.stack,
                promise
            });
        });
        
        // Uncaught exceptions
        process.on('uncaughtException', (error) => {
            frontendLogger.error('❌ Uncaught Exception', {
                error: error.message,
                stack: error.stack
            });
            process.exit(1);
        });
        
        return server;
        
    } catch (error) {
        frontendLogger.error('❌ Failed to start frontend server', {
            error: error.message,
            stack: error.stack
        });
        process.exit(1);
    }
}

// Check if this script is being run directly
if (import.meta.url === `file://${process.argv[1]}`) {
    startFrontendServer();
}

export default {
    createFrontendServer,
    startFrontendServer
};
