<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        .navbar-brand {
            font-weight: bold;
        }
        
        .status-healthy {
            color: #28a745;
        }
        
        .status-warning {
            color: #ffc107;
        }
        
        .status-error {
            color: #dc3545;
        }
        
        .loading {
            display: none;
        }
        
        .loading.show {
            display: block;
        }
        
        .api-error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 1rem;
            border-radius: 0.375rem;
            margin: 1rem 0;
        }
        
        .job-status-running {
            color: #0d6efd;
        }
        
        .job-status-completed {
            color: #198754;
        }
        
        .job-status-failed {
            color: #dc3545;
        }
        
        .job-status-cancelled {
            color: #6c757d;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">DSV Scraper</a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/rankings">Rankings</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/events">Events</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/jobs">Jobs</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/system">System</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <span class="navbar-text" id="system-status">
                            <span class="spinner-border spinner-border-sm" role="status"></span>
                            Checking...
                        </span>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <main class="container mt-4">
        <%- body %>
    </main>
    
    <!-- Footer -->
    <footer class="bg-light mt-5 py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-0">&copy; 2025 DSV Scraper. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-end">
                    <small class="text-muted">
                        API Endpoint: <code><%= apiEndpoint %></code>
                    </small>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- API Client Library -->
    <script>
        // Global API client
        class ApiClient {
            constructor(baseUrl) {
                this.baseUrl = baseUrl;
            }
            
            async request(method, endpoint, data = null) {
                const url = `${this.baseUrl}${endpoint}`;
                const options = {
                    method,
                    headers: {
                        'Content-Type': 'application/json',
                    }
                };
                
                if (data) {
                    options.body = JSON.stringify(data);
                }
                
                try {
                    const response = await fetch(url, options);
                    const result = await response.json();
                    
                    if (!response.ok) {
                        throw new Error(result.error || `HTTP ${response.status}`);
                    }
                    
                    return result;
                } catch (error) {
                    console.error('API request failed:', error);
                    throw error;
                }
            }
            
            async get(endpoint) {
                return this.request('GET', endpoint);
            }
            
            async post(endpoint, data) {
                return this.request('POST', endpoint, data);
            }
            
            async put(endpoint, data) {
                return this.request('PUT', endpoint, data);
            }
            
            async delete(endpoint) {
                return this.request('DELETE', endpoint);
            }
        }
        
        // Initialize API client
        const api = new ApiClient('<%= apiEndpoint %>');
        
        // Utility functions
        function showLoading(elementId) {
            const element = document.getElementById(elementId);
            if (element) {
                element.classList.add('show');
            }
        }
        
        function hideLoading(elementId) {
            const element = document.getElementById(elementId);
            if (element) {
                element.classList.remove('show');
            }
        }
        
        function showError(containerId, message) {
            const container = document.getElementById(containerId);
            if (container) {
                container.innerHTML = `
                    <div class="api-error">
                        <strong>Error:</strong> ${message}
                    </div>
                `;
            }
        }
        
        function formatDateTime(dateString) {
            if (!dateString) return 'N/A';
            return new Date(dateString).toLocaleString();
        }
        
        function formatDuration(ms) {
            if (!ms) return 'N/A';
            const seconds = Math.floor(ms / 1000);
            const minutes = Math.floor(seconds / 60);
            const hours = Math.floor(minutes / 60);
            
            if (hours > 0) {
                return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
            } else if (minutes > 0) {
                return `${minutes}m ${seconds % 60}s`;
            } else {
                return `${seconds}s`;
            }
        }
        
        function getStatusClass(status) {
            switch (status) {
                case 'healthy':
                case 'completed':
                case 'success':
                    return 'status-healthy';
                case 'warning':
                case 'running':
                    return 'status-warning';
                case 'error':
                case 'failed':
                case 'unhealthy':
                    return 'status-error';
                default:
                    return '';
            }
        }
        
        // Check system status on page load
        async function checkSystemStatus() {
            try {
                const health = await api.get('/system/health');
                const statusElement = document.getElementById('system-status');
                
                if (statusElement) {
                    const statusClass = getStatusClass(health.status);
                    statusElement.innerHTML = `
                        <span class="${statusClass}">
                            ● ${health.status.toUpperCase()}
                        </span>
                    `;
                }
            } catch (error) {
                const statusElement = document.getElementById('system-status');
                if (statusElement) {
                    statusElement.innerHTML = `
                        <span class="status-error">
                            ● ERROR
                        </span>
                    `;
                }
            }
        }
        
        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            checkSystemStatus();
            
            // Check status every 30 seconds
            setInterval(checkSystemStatus, 30000);
        });
    </script>
    
    <!-- Page-specific scripts -->
    <% if (typeof pageScript !== 'undefined') { %>
        <%- pageScript %>
    <% } %>
</body>
</html>
