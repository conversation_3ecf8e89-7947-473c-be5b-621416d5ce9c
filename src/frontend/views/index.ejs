<%- include('layout', { 
    title: title,
    apiEndpoint: apiEndpoint,
    body: `
        <div class="row">
            <div class="col-12">
                <h1>DSV Scraper Dashboard</h1>
                <p class="lead">Monitor and manage your swimming rankings scraping operations.</p>
            </div>
        </div>
        
        <!-- System Status Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body text-center">
                        <h5 class="card-title">System Status</h5>
                        <div id="system-status-card" class="loading">
                            <div class="spinner-border" role="status"></div>
                        </div>
                        <div id="system-status-content"></div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body text-center">
                        <h5 class="card-title">Active Jobs</h5>
                        <div id="active-jobs-card" class="loading">
                            <div class="spinner-border" role="status"></div>
                        </div>
                        <div id="active-jobs-content"></div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body text-center">
                        <h5 class="card-title">Total Rankings</h5>
                        <div id="rankings-count-card" class="loading">
                            <div class="spinner-border" role="status"></div>
                        </div>
                        <div id="rankings-count-content"></div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body text-center">
                        <h5 class="card-title">Last Scrape</h5>
                        <div id="last-scrape-card" class="loading">
                            <div class="spinner-border" role="status"></div>
                        </div>
                        <div id="last-scrape-content"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Scraping Operations</h6>
                                <button class="btn btn-primary me-2" onclick="startFullScraping()">
                                    Start Full Scraping
                                </button>
                                <button class="btn btn-outline-primary me-2" onclick="showEventScrapingModal()">
                                    Scrape Single Event
                                </button>
                                <button class="btn btn-outline-secondary" onclick="viewActiveJobs()">
                                    View Active Jobs
                                </button>
                            </div>
                            <div class="col-md-6">
                                <h6>Data Management</h6>
                                <button class="btn btn-success me-2" onclick="exportData()">
                                    Export CSV
                                </button>
                                <button class="btn btn-warning me-2" onclick="startCleanup()">
                                    Cleanup Old Data
                                </button>
                                <button class="btn btn-info" onclick="viewSystemStatus()">
                                    System Status
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Recent Activity -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Recent Activity</h5>
                    </div>
                    <div class="card-body">
                        <div id="recent-activity-loading" class="loading text-center">
                            <div class="spinner-border" role="status"></div>
                            <p>Loading recent activity...</p>
                        </div>
                        <div id="recent-activity-content"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Event Scraping Modal -->
        <div class="modal fade" id="eventScrapingModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Scrape Single Event</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="eventScrapingForm">
                            <div class="mb-3">
                                <label for="eventName" class="form-label">Event Name</label>
                                <input type="text" class="form-control" id="eventName" required>
                            </div>
                            <div class="mb-3">
                                <label for="ageGroup" class="form-label">Age Group</label>
                                <input type="text" class="form-control" id="ageGroup" placeholder="e.g., 2015" required>
                            </div>
                            <div class="mb-3">
                                <label for="gender" class="form-label">Gender</label>
                                <select class="form-control" id="gender" required>
                                    <option value="">Select Gender</option>
                                    <option value="M">Male (M)</option>
                                    <option value="W">Female (W)</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="regionId" class="form-label">Region ID (Optional)</label>
                                <input type="number" class="form-control" id="regionId" min="1" max="18" placeholder="1-18">
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" onclick="submitEventScraping()">Start Scraping</button>
                    </div>
                </div>
            </div>
        </div>
    `,
    pageScript: `
        <script>
            // Dashboard-specific functions
            async function loadDashboardData() {
                await Promise.all([
                    loadSystemStatus(),
                    loadActiveJobs(),
                    loadRankingsCount(),
                    loadLastScrape(),
                    loadRecentActivity()
                ]);
            }
            
            async function loadSystemStatus() {
                try {
                    const health = await api.get('/system/health');
                    const content = document.getElementById('system-status-content');
                    const statusClass = getStatusClass(health.status);
                    
                    content.innerHTML = \`
                        <h3 class="\${statusClass}">\${health.status.toUpperCase()}</h3>
                        <small>Uptime: \${formatDuration(health.uptime * 1000)}</small>
                    \`;
                } catch (error) {
                    showError('system-status-content', error.message);
                } finally {
                    hideLoading('system-status-card');
                }
            }
            
            async function loadActiveJobs() {
                try {
                    const [scrapingJobs, processingJobs] = await Promise.all([
                        api.get('/scraping/jobs'),
                        api.get('/jobs/processing/active')
                    ]);
                    
                    const totalJobs = scrapingJobs.count + processingJobs.count;
                    const content = document.getElementById('active-jobs-content');
                    
                    content.innerHTML = \`
                        <h3>\${totalJobs}</h3>
                        <small>Scraping: \${scrapingJobs.count}, Processing: \${processingJobs.count}</small>
                    \`;
                } catch (error) {
                    showError('active-jobs-content', error.message);
                } finally {
                    hideLoading('active-jobs-card');
                }
            }
            
            async function loadRankingsCount() {
                try {
                    const stats = await api.get('/data/statistics');
                    const content = document.getElementById('rankings-count-content');
                    
                    content.innerHTML = \`
                        <h3>\${stats.data.totalRankings || 0}</h3>
                        <small>Total rankings in database</small>
                    \`;
                } catch (error) {
                    showError('rankings-count-content', error.message);
                } finally {
                    hideLoading('rankings-count-card');
                }
            }
            
            async function loadLastScrape() {
                try {
                    const history = await api.get('/scraping/history?limit=1');
                    const content = document.getElementById('last-scrape-content');
                    
                    if (history.history.length > 0) {
                        const lastScrape = history.history[0];
                        const statusClass = getStatusClass(lastScrape.status);
                        
                        content.innerHTML = \`
                            <div class="\${statusClass}">\${lastScrape.status.toUpperCase()}</div>
                            <small>\${formatDateTime(lastScrape.endTime)}</small>
                        \`;
                    } else {
                        content.innerHTML = \`
                            <h3>-</h3>
                            <small>No scraping history</small>
                        \`;
                    }
                } catch (error) {
                    showError('last-scrape-content', error.message);
                } finally {
                    hideLoading('last-scrape-card');
                }
            }
            
            async function loadRecentActivity() {
                try {
                    const [scrapingHistory, jobHistory] = await Promise.all([
                        api.get('/scraping/history?limit=5'),
                        api.get('/jobs/history?limit=5')
                    ]);
                    
                    const content = document.getElementById('recent-activity-content');
                    let html = '<div class="list-group">';
                    
                    // Combine and sort activities
                    const activities = [
                        ...scrapingHistory.history.map(h => ({ ...h, type: 'scraping' })),
                        ...jobHistory.data.map(h => ({ ...h, type: 'job' }))
                    ].sort((a, b) => new Date(b.endTime || b.startTime) - new Date(a.endTime || a.startTime));
                    
                    activities.slice(0, 10).forEach(activity => {
                        const statusClass = getStatusClass(activity.status);
                        html += \`
                            <div class="list-group-item">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">\${activity.type === 'scraping' ? 'Scraping' : 'Job'}: \${activity.jobName || activity.jobId}</h6>
                                    <small>\${formatDateTime(activity.endTime || activity.startTime)}</small>
                                </div>
                                <p class="mb-1 \${statusClass}">Status: \${activity.status}</p>
                            </div>
                        \`;
                    });
                    
                    html += '</div>';
                    content.innerHTML = html;
                } catch (error) {
                    showError('recent-activity-content', error.message);
                } finally {
                    hideLoading('recent-activity-loading');
                }
            }
            
            // Action functions
            async function startFullScraping() {
                if (confirm('Start full scraping of all events? This may take a while.')) {
                    try {
                        const result = await api.post('/scraping/all');
                        alert(\`Full scraping started successfully. Job ID: \${result.jobId}\`);
                        loadDashboardData(); // Refresh data
                    } catch (error) {
                        alert(\`Failed to start full scraping: \${error.message}\`);
                    }
                }
            }
            
            function showEventScrapingModal() {
                const modal = new bootstrap.Modal(document.getElementById('eventScrapingModal'));
                modal.show();
            }
            
            async function submitEventScraping() {
                const form = document.getElementById('eventScrapingForm');
                const formData = new FormData(form);
                
                const data = {
                    eventName: document.getElementById('eventName').value,
                    ageGroup: document.getElementById('ageGroup').value,
                    gender: document.getElementById('gender').value,
                    regionId: document.getElementById('regionId').value ? parseInt(document.getElementById('regionId').value) : undefined
                };
                
                try {
                    const result = await api.post('/scraping/event', data);
                    alert(\`Event scraping started successfully. Job ID: \${result.jobId}\`);
                    
                    // Close modal and refresh data
                    const modal = bootstrap.Modal.getInstance(document.getElementById('eventScrapingModal'));
                    modal.hide();
                    form.reset();
                    loadDashboardData();
                } catch (error) {
                    alert(\`Failed to start event scraping: \${error.message}\`);
                }
            }
            
            function viewActiveJobs() {
                window.location.href = '/jobs';
            }
            
            function exportData() {
                window.open('/api/data/export/csv', '_blank');
            }
            
            async function startCleanup() {
                if (confirm('Start cleanup of old data? This will remove data older than 365 days.')) {
                    try {
                        const result = await api.post('/jobs/processing/cleanup', { olderThanDays: 365 });
                        alert(\`Cleanup job started successfully. Job ID: \${result.jobId}\`);
                        loadDashboardData(); // Refresh data
                    } catch (error) {
                        alert(\`Failed to start cleanup: \${error.message}\`);
                    }
                }
            }
            
            function viewSystemStatus() {
                window.location.href = '/system';
            }
            
            // Initialize dashboard
            document.addEventListener('DOMContentLoaded', function() {
                loadDashboardData();
                
                // Refresh data every 30 seconds
                setInterval(loadDashboardData, 30000);
            });
        </script>
    `
}) %>
