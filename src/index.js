/**
 * Legacy Entry Point for DSV Scraper - Redirects to New Modular Architecture
 *
 * This file maintains backward compatibility while directing users to the new
 * modular architecture. For new deployments, use the individual services:
 * - Backend: src/backend/index.js
 * - API: src/api/index.js
 * - Frontend: src/frontend/index.js
 */
import config from './config.js';
import { webLogger } from './utils/logger.js';

/**
 * Legacy Appwrite Function Entry Point
 *
 * @deprecated Use the new modular architecture instead:
 * - Deploy backend, API, and frontend services separately
 * - See docs/DEPLOYMENT.md for modern deployment options
 */
export async function main(req, res) {
    webLogger.warn('⚠️ Using deprecated legacy entry point. Please migrate to modular architecture.');

    const message = {
        error: 'Legacy Entry Point Deprecated',
        message: 'This entry point has been replaced by the new modular architecture.',
        migration: {
            backend: 'Use src/backend/index.js for core services',
            api: 'Use src/api/index.js for REST API',
            frontend: 'Use src/frontend/index.js for web interface'
        },
        documentation: 'See README-NEW-ARCHITECTURE.md and docs/ for migration guide'
    };

    if (res && res.send) {
        return res.send(JSON.stringify(message, null, 2), 410); // 410 Gone
    }

    return message;
}

/**
 * Legacy Standalone Server
 *
 * @deprecated Use the new modular services instead:
 * - npm run dev:all (all services)
 * - npm run backend:dev (backend only)
 * - npm run api:dev (API only)
 * - npm run frontend:dev (frontend only)
 */
async function startLegacyServer() {
    webLogger.warn('⚠️ Legacy standalone server is deprecated.');
    webLogger.info('📖 Please use the new modular architecture:');
    webLogger.info('   npm run dev:all        # Start all services');
    webLogger.info('   npm run backend:dev    # Backend service');
    webLogger.info('   npm run api:dev        # API service');
    webLogger.info('   npm run frontend:dev   # Frontend service');
    webLogger.info('📚 See README-NEW-ARCHITECTURE.md for details');

    process.exit(1);
}

// Check if script is run directly
if (import.meta.url === `file://${process.argv[1]}`) {
    startLegacyServer();
}

export default main;
