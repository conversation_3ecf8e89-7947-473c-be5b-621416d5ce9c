/**
 * Logging-Konfiguration für DSV Scraper
 */
import winston from 'winston';
import DailyRotateFile from 'winston-daily-rotate-file';
import path from 'path';
import fs from 'fs';
import config from '../config.js';

// <PERSON><PERSON> sicher, dass das Logs-Verzeichnis existiert
if (!fs.existsSync(config.LOGS_DIR)) {
    fs.mkdirSync(config.LOGS_DIR, { recursive: true });
}

// Custom Format für bessere Lesbarkeit
const customFormat = winston.format.combine(
    winston.format.timestamp({
        format: 'YYYY-MM-DD HH:mm:ss'
    }),
    winston.format.errors({ stack: true }),
    winston.format.printf(({ level, message, timestamp, stack, ...meta }) => {
        let log = `${timestamp} [${level.toUpperCase()}]: ${message}`;
        
        // Füge Stack-Trace hinzu wenn vorhanden
        if (stack) {
            log += `\n${stack}`;
        }
        
        // Füge zusätzliche Metadaten hinzu
        if (Object.keys(meta).length > 0) {
            log += `\n${JSON.stringify(meta, null, 2)}`;
        }
        
        return log;
    })
);

// Console Format für Development
const consoleFormat = winston.format.combine(
    winston.format.colorize(),
    winston.format.timestamp({
        format: 'HH:mm:ss'
    }),
    winston.format.printf(({ level, message, timestamp, ...meta }) => {
        let log = `${timestamp} ${level}: ${message}`;
        
        if (Object.keys(meta).length > 0) {
            log += ` ${JSON.stringify(meta)}`;
        }
        
        return log;
    })
);

// Transports konfigurieren
const transports = [];

// Console Transport (immer aktiv)
transports.push(
    new winston.transports.Console({
        level: config.NODE_ENV === 'development' ? 'debug' : config.LOG_LEVEL,
        format: config.NODE_ENV === 'development' ? consoleFormat : customFormat,
        handleExceptions: true,
        handleRejections: true
    })
);

// File Transport für Production
if (config.NODE_ENV === 'production') {
    // Allgemeine Logs
    transports.push(
        new DailyRotateFile({
            filename: path.join(config.LOGS_DIR, 'dsv-scraper-%DATE%.log'),
            datePattern: 'YYYY-MM-DD',
            maxSize: '20m',
            maxFiles: '14d',
            level: config.LOG_LEVEL,
            format: customFormat,
            handleExceptions: true,
            handleRejections: true
        })
    );
    
    // Error Logs separat
    transports.push(
        new DailyRotateFile({
            filename: path.join(config.LOGS_DIR, 'error-%DATE%.log'),
            datePattern: 'YYYY-MM-DD',
            maxSize: '20m',
            maxFiles: '30d',
            level: 'error',
            format: customFormat,
            handleExceptions: true,
            handleRejections: true
        })
    );
    
    // Scraping Logs separat
    transports.push(
        new DailyRotateFile({
            filename: path.join(config.LOGS_DIR, 'scraping-%DATE%.log'),
            datePattern: 'YYYY-MM-DD',
            maxSize: '50m',
            maxFiles: '7d',
            level: 'info',
            format: customFormat
        })
    );
}

// Logger erstellen
const logger = winston.createLogger({
    level: config.LOG_LEVEL,
    format: customFormat,
    transports,
    exitOnError: false
});

// Spezielle Logger für verschiedene Module
export const webLogger = logger.child({ module: 'web' });
export const scraperLogger = logger.child({ module: 'scraper' });
export const schedulerLogger = logger.child({ module: 'scheduler' });
export const databaseLogger = logger.child({ module: 'database' });
export const backendLogger = logger.child({ module: 'backend' });
export const apiLogger = logger.child({ module: 'api' });
export const frontendLogger = logger.child({ module: 'frontend' });

// Helper-Funktionen
export const logScrapingStart = (eventName, config) => {
    scraperLogger.info('🚀 Starte Scraping', {
        event: eventName,
        region: config.region_id,
        ageGroup: config.age_group,
        gender: config.gender
    });
};

export const logScrapingSuccess = (eventName, resultCount, duration) => {
    scraperLogger.info('✅ Scraping erfolgreich', {
        event: eventName,
        resultCount,
        duration: `${duration}ms`
    });
};

export const logScrapingError = (eventName, error, config) => {
    scraperLogger.error('❌ Scraping fehlgeschlagen', {
        event: eventName,
        error: error.message,
        stack: error.stack,
        config
    });
};

export const logDatabaseOperation = (operation, table, data) => {
    databaseLogger.debug('📊 Datenbankoperation', {
        operation,
        table,
        data: typeof data === 'object' ? Object.keys(data) : data
    });
};

export const logWebRequest = (req, res, duration) => {
    webLogger.info('🌐 HTTP Request', {
        method: req.method,
        url: req.url,
        status: res.statusCode,
        duration: `${duration}ms`,
        userAgent: req.get('User-Agent'),
        ip: req.ip
    });
};

export const logSchedulerJob = (jobName, status, details) => {
    schedulerLogger.info(`⏰ Scheduler Job: ${jobName}`, {
        status,
        details
    });
};

export default logger;
