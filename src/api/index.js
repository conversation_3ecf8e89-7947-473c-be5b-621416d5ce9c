/**
 * REST API Server Entry Point
 * 
 * This module provides a standalone REST API server that serves as the
 * interface between frontend and backend services. It handles:
 * - HTTP request routing
 * - Authentication and authorization
 * - Request validation
 * - Response formatting
 * - Error handling
 */

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import rateLimit from 'express-rate-limit';

import config from '../config.js';
import { apiLogger } from '../utils/logger.js';
import { startBackendService, getBackendService } from '../backend/index.js';

// Import API routes
import scrapingRoutes from './routes/scraping.js';
import dataRoutes from './routes/data.js';
import jobsRoutes from './routes/jobs.js';
import systemRoutes from './routes/system.js';

/**
 * Create and configure the API server
 */
export function createApiServer() {
    const app = express();
    
    // Trust proxy for correct IP addresses
    app.set('trust proxy', 1);
    
    // Security middleware
    app.use(helmet({
        contentSecurityPolicy: false // API doesn't serve HTML
    }));
    
    // CORS configuration
    app.use(cors({
        origin: config.CORS_ORIGINS,
        credentials: true,
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
        allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
    }));
    
    // Rate limiting
    const limiter = rateLimit({
        windowMs: config.RATE_LIMIT_WINDOW,
        max: config.RATE_LIMIT_MAX,
        message: {
            error: 'Too many requests. Please try again later.',
            retryAfter: Math.ceil(config.RATE_LIMIT_WINDOW / 1000)
        },
        standardHeaders: true,
        legacyHeaders: false
    });
    app.use(limiter);
    
    // Body parser middleware
    app.use(express.json({ limit: '10mb' }));
    app.use(express.urlencoded({ extended: true, limit: '10mb' }));
    
    // Compression
    app.use(compression());
    
    // Request logging middleware
    app.use((req, res, next) => {
        const startTime = Date.now();
        
        res.on('finish', () => {
            const duration = Date.now() - startTime;
            apiLogger.info('API Request', {
                method: req.method,
                url: req.url,
                status: res.statusCode,
                duration: `${duration}ms`,
                ip: req.ip,
                userAgent: req.get('User-Agent')
            });
        });
        
        next();
    });
    
    // Backend service middleware
    app.use(async (req, res, next) => {
        try {
            const backendService = getBackendService();
            if (!backendService) {
                return res.status(503).json({
                    error: 'Backend service not available',
                    message: 'The backend service is not running'
                });
            }
            
            req.backend = backendService;
            next();
            
        } catch (error) {
            apiLogger.error('Backend service middleware error', {
                error: error.message,
                url: req.url
            });
            res.status(503).json({
                error: 'Backend service error',
                message: 'Unable to connect to backend service'
            });
        }
    });
    
    // API routes
    app.use('/api/scraping', scrapingRoutes);
    app.use('/api/data', dataRoutes);
    app.use('/api/jobs', jobsRoutes);
    app.use('/api/system', systemRoutes);
    
    // Root endpoint
    app.get('/', (req, res) => {
        res.json({
            name: 'DSV Scraper API',
            version: process.env.npm_package_version || '1.0.0',
            status: 'running',
            timestamp: new Date().toISOString(),
            endpoints: {
                scraping: '/api/scraping',
                data: '/api/data',
                jobs: '/api/jobs',
                system: '/api/system'
            }
        });
    });
    
    // Health check endpoint
    app.get('/health', async (req, res) => {
        try {
            const backendService = getBackendService();
            if (!backendService) {
                return res.status(503).json({
                    status: 'unhealthy',
                    message: 'Backend service not available',
                    timestamp: new Date().toISOString()
                });
            }
            
            const healthStatus = await backendService.healthCheck();
            
            res.status(healthStatus.status === 'healthy' ? 200 : 503).json({
                ...healthStatus,
                api: {
                    status: 'healthy',
                    uptime: process.uptime(),
                    memory: process.memoryUsage()
                }
            });
            
        } catch (error) {
            res.status(503).json({
                status: 'unhealthy',
                timestamp: new Date().toISOString(),
                error: error.message
            });
        }
    });
    
    // 404 handler
    app.use((req, res) => {
        res.status(404).json({
            error: 'API endpoint not found',
            path: req.path,
            method: req.method,
            availableEndpoints: [
                '/api/scraping',
                '/api/data',
                '/api/jobs',
                '/api/system'
            ]
        });
    });
    
    // Error handler
    app.use((error, req, res, next) => {
        apiLogger.error('Unhandled API error', {
            error: error.message,
            stack: error.stack,
            url: req.url,
            method: req.method,
            ip: req.ip
        });
        
        const status = error.status || 500;
        const message = config.NODE_ENV === 'development' ? error.message : 'Internal server error';
        
        res.status(status).json({
            error: message,
            ...(config.NODE_ENV === 'development' && { stack: error.stack })
        });
    });
    
    return app;
}

/**
 * Start the API server
 */
export async function startApiServer() {
    try {
        apiLogger.info('🚀 Starting DSV API Server');
        
        // Start backend service first
        await startBackendService();
        
        // Create and start API server
        const app = createApiServer();
        const port = config.API_PORT || config.PORT || 3000;
        
        const server = app.listen(port, () => {
            apiLogger.info('✅ DSV API Server started', {
                port,
                environment: config.NODE_ENV,
                pid: process.pid
            });
        });
        
        // Graceful shutdown
        const gracefulShutdown = async (signal) => {
            apiLogger.info(`📴 ${signal} received. Starting graceful shutdown...`);
            
            server.close(async () => {
                apiLogger.info('🔒 API server closed');
                
                // Stop backend service
                const { stopBackendService } = await import('../backend/index.js');
                await stopBackendService();
                
                apiLogger.info('✅ Graceful shutdown completed');
                process.exit(0);
            });
            
            // Force shutdown after 30 seconds
            setTimeout(() => {
                apiLogger.error('⚠️ Force shutdown after timeout');
                process.exit(1);
            }, 30000);
        };
        
        // Signal handlers
        process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
        process.on('SIGINT', () => gracefulShutdown('SIGINT'));
        
        // Unhandled promise rejections
        process.on('unhandledRejection', (reason, promise) => {
            apiLogger.error('❌ Unhandled Promise Rejection', {
                reason: reason?.message || reason,
                stack: reason?.stack,
                promise
            });
        });
        
        // Uncaught exceptions
        process.on('uncaughtException', (error) => {
            apiLogger.error('❌ Uncaught Exception', {
                error: error.message,
                stack: error.stack
            });
            process.exit(1);
        });
        
        return server;
        
    } catch (error) {
        apiLogger.error('❌ Failed to start API server', {
            error: error.message,
            stack: error.stack
        });
        process.exit(1);
    }
}

// Check if this script is being run directly
if (import.meta.url === `file://${process.argv[1]}`) {
    startApiServer();
}

export default {
    createApiServer,
    startApiServer
};
