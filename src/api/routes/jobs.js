/**
 * Jobs API Routes
 * 
 * Handles all job-related API endpoints:
 * - Manage scheduled jobs
 * - Monitor job execution
 * - Get job history
 * - Control job scheduler
 */

import express from 'express';
import { body, query, validationResult } from 'express-validator';
import { apiLogger } from '../../utils/logger.js';

const router = express.Router();

// Validation error handler
const handleValidationErrors = (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            error: 'Validation error',
            details: errors.array()
        });
    }
    next();
};

/**
 * GET /api/jobs/scheduled - Get all scheduled jobs
 */
router.get('/scheduled', async (req, res) => {
    try {
        const jobScheduler = req.backend.getJobSchedulerService();
        const scheduledJobs = jobScheduler.getScheduledJobs();
        
        res.json({
            success: true,
            data: scheduledJobs,
            count: scheduledJobs.length
        });
        
    } catch (error) {
        apiLogger.error('Scheduled jobs API error', {
            error: error.message
        });
        
        res.status(500).json({
            error: 'Failed to retrieve scheduled jobs',
            message: error.message
        });
    }
});

/**
 * POST /api/jobs/scheduled/:jobName/execute - Execute a scheduled job manually
 */
router.post('/scheduled/:jobName/execute', async (req, res) => {
    try {
        const { jobName } = req.params;
        
        const jobScheduler = req.backend.getJobSchedulerService();
        const result = await jobScheduler.executeJobManually(jobName);
        
        res.json({
            success: true,
            message: 'Job executed successfully',
            jobName,
            result
        });
        
    } catch (error) {
        apiLogger.error('Manual job execution API error', {
            error: error.message,
            jobName: req.params.jobName
        });
        
        res.status(500).json({
            error: 'Failed to execute job',
            message: error.message
        });
    }
});

/**
 * PUT /api/jobs/scheduled/:jobName/toggle - Enable/disable a scheduled job
 */
router.put('/scheduled/:jobName/toggle', [
    body('enabled').isBoolean().withMessage('Enabled must be a boolean')
], handleValidationErrors, async (req, res) => {
    try {
        const { jobName } = req.params;
        const { enabled } = req.body;
        
        const jobScheduler = req.backend.getJobSchedulerService();
        const success = jobScheduler.toggleJob(jobName, enabled);
        
        if (!success) {
            return res.status(404).json({
                error: 'Job not found',
                jobName
            });
        }
        
        res.json({
            success: true,
            message: `Job ${enabled ? 'enabled' : 'disabled'} successfully`,
            jobName,
            enabled
        });
        
    } catch (error) {
        apiLogger.error('Toggle job API error', {
            error: error.message,
            jobName: req.params.jobName
        });
        
        res.status(500).json({
            error: 'Failed to toggle job',
            message: error.message
        });
    }
});

/**
 * GET /api/jobs/history - Get job execution history
 */
router.get('/history', [
    query('limit').optional().isInt({ min: 1, max: 100 }).toInt()
], handleValidationErrors, async (req, res) => {
    try {
        const { limit = 20 } = req.query;
        
        const jobScheduler = req.backend.getJobSchedulerService();
        const history = jobScheduler.getJobHistory(limit);
        
        res.json({
            success: true,
            data: history,
            count: history.length
        });
        
    } catch (error) {
        apiLogger.error('Job history API error', {
            error: error.message
        });
        
        res.status(500).json({
            error: 'Failed to retrieve job history',
            message: error.message
        });
    }
});

/**
 * GET /api/jobs/processing/active - Get active data processing jobs
 */
router.get('/processing/active', async (req, res) => {
    try {
        const dataProcessingService = req.backend.getDataProcessingService();
        const activeJobs = dataProcessingService.getActiveJobs();
        
        res.json({
            success: true,
            data: activeJobs,
            count: activeJobs.length
        });
        
    } catch (error) {
        apiLogger.error('Active processing jobs API error', {
            error: error.message
        });
        
        res.status(500).json({
            error: 'Failed to retrieve active processing jobs',
            message: error.message
        });
    }
});

/**
 * GET /api/jobs/processing/history - Get data processing job history
 */
router.get('/processing/history', [
    query('limit').optional().isInt({ min: 1, max: 100 }).toInt()
], handleValidationErrors, async (req, res) => {
    try {
        const { limit = 20 } = req.query;
        
        const dataProcessingService = req.backend.getDataProcessingService();
        const history = dataProcessingService.getProcessingHistory(limit);
        
        res.json({
            success: true,
            data: history,
            count: history.length
        });
        
    } catch (error) {
        apiLogger.error('Processing history API error', {
            error: error.message
        });
        
        res.status(500).json({
            error: 'Failed to retrieve processing history',
            message: error.message
        });
    }
});

/**
 * POST /api/jobs/processing/cleanup - Start data cleanup job
 */
router.post('/processing/cleanup', [
    body('olderThanDays').optional().isInt({ min: 1 }).withMessage('olderThanDays must be a positive integer')
], handleValidationErrors, async (req, res) => {
    try {
        const { olderThanDays = 365 } = req.body;
        
        const dataProcessingService = req.backend.getDataProcessingService();
        const result = await dataProcessingService.cleanOldData({ olderThanDays });
        
        res.json({
            success: true,
            message: 'Cleanup job started',
            jobId: result.jobId,
            timestamp: new Date().toISOString()
        });
        
    } catch (error) {
        apiLogger.error('Cleanup job API error', {
            error: error.message
        });
        
        res.status(500).json({
            error: 'Failed to start cleanup job',
            message: error.message
        });
    }
});

/**
 * GET /api/jobs/status/:jobId - Get status of any job (scraping or processing)
 */
router.get('/status/:jobId', async (req, res) => {
    try {
        const { jobId } = req.params;
        
        // Check scraping service first
        const scrapingService = req.backend.getScrapingService();
        let status = scrapingService.getJobStatus(jobId);
        
        // If not found in scraping, check data processing
        if (!status) {
            const dataProcessingService = req.backend.getDataProcessingService();
            status = dataProcessingService.getJobStatus(jobId);
        }
        
        if (!status) {
            return res.status(404).json({
                error: 'Job not found',
                jobId
            });
        }
        
        res.json({
            success: true,
            jobId,
            status
        });
        
    } catch (error) {
        apiLogger.error('Job status API error', {
            error: error.message,
            jobId: req.params.jobId
        });
        
        res.status(500).json({
            error: 'Failed to get job status',
            message: error.message
        });
    }
});

/**
 * GET /api/jobs/health - Get job scheduler health
 */
router.get('/health', async (req, res) => {
    try {
        const jobScheduler = req.backend.getJobSchedulerService();
        const health = await jobScheduler.healthCheck();
        
        res.json({
            success: true,
            health
        });
        
    } catch (error) {
        apiLogger.error('Job scheduler health API error', {
            error: error.message
        });
        
        res.status(500).json({
            error: 'Failed to get job scheduler health',
            message: error.message
        });
    }
});

export default router;
