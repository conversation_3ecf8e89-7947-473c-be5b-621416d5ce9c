/**
 * System API Routes
 * 
 * Handles all system-related API endpoints:
 * - System health checks
 * - Service status monitoring
 * - Configuration management
 * - Logs and diagnostics
 */

import express from 'express';
import { query, validationResult } from 'express-validator';
import { apiLogger } from '../../utils/logger.js';
import config from '../../config.js';

const router = express.Router();

// Validation error handler
const handleValidationErrors = (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            error: 'Validation error',
            details: errors.array()
        });
    }
    next();
};

/**
 * GET /api/system/health - Comprehensive system health check
 */
router.get('/health', async (req, res) => {
    try {
        const backendService = req.backend;
        const healthStatus = await backendService.healthCheck();
        
        // Add API-specific health information
        const systemHealth = {
            ...healthStatus,
            api: {
                status: 'healthy',
                uptime: process.uptime(),
                memory: process.memoryUsage(),
                version: process.env.npm_package_version || '1.0.0',
                environment: config.NODE_ENV,
                pid: process.pid
            },
            timestamp: new Date().toISOString()
        };
        
        const httpStatus = systemHealth.status === 'healthy' ? 200 : 503;
        res.status(httpStatus).json(systemHealth);
        
    } catch (error) {
        apiLogger.error('System health API error', {
            error: error.message
        });
        
        res.status(503).json({
            status: 'error',
            timestamp: new Date().toISOString(),
            error: error.message
        });
    }
});

/**
 * GET /api/system/status - Get detailed system status
 */
router.get('/status', async (req, res) => {
    try {
        const backendService = req.backend;
        const stats = backendService.getStats();
        
        // Get service-specific status
        const database = backendService.getDatabase();
        const scrapingService = backendService.getScrapingService();
        const dataProcessingService = backendService.getDataProcessingService();
        const jobSchedulerService = backendService.getJobSchedulerService();
        
        const systemStatus = {
            backend: stats,
            services: {
                database: {
                    isConnected: database?.isConnected || false,
                    connectionRetries: database?.connectionRetries || 0
                },
                scraping: {
                    isInitialized: scrapingService?.isInitialized || false,
                    activeJobs: scrapingService?.activeScrapeJobs?.size || 0,
                    historySize: scrapingService?.scrapeHistory?.length || 0
                },
                dataProcessing: {
                    isInitialized: dataProcessingService?.isInitialized || false,
                    activeJobs: dataProcessingService?.activeProcessingJobs?.size || 0,
                    historySize: dataProcessingService?.processingHistory?.length || 0
                },
                jobScheduler: {
                    isInitialized: jobSchedulerService?.isInitialized || false,
                    isRunning: jobSchedulerService?.isRunning || false,
                    scheduledJobsCount: jobSchedulerService?.scheduledJobs?.size || 0
                }
            },
            system: {
                uptime: process.uptime(),
                memory: process.memoryUsage(),
                cpu: process.cpuUsage(),
                version: process.version,
                platform: process.platform,
                arch: process.arch
            },
            timestamp: new Date().toISOString()
        };
        
        res.json({
            success: true,
            data: systemStatus
        });
        
    } catch (error) {
        apiLogger.error('System status API error', {
            error: error.message
        });
        
        res.status(500).json({
            error: 'Failed to get system status',
            message: error.message
        });
    }
});

/**
 * GET /api/system/config - Get system configuration (sanitized)
 */
router.get('/config', async (req, res) => {
    try {
        // Return sanitized configuration (no sensitive data)
        const sanitizedConfig = {
            NODE_ENV: config.NODE_ENV,
            PORT: config.PORT,
            API_PORT: config.API_PORT,
            ENABLE_SCHEDULER: config.ENABLE_SCHEDULER,
            ENABLE_WEEKLY_SCRAPING: config.ENABLE_WEEKLY_SCRAPING,
            ENABLE_DAILY_CLEANUP: config.ENABLE_DAILY_CLEANUP,
            ENABLE_HEALTH_CHECKS: config.ENABLE_HEALTH_CHECKS,
            RATE_LIMIT_WINDOW: config.RATE_LIMIT_WINDOW,
            RATE_LIMIT_MAX: config.RATE_LIMIT_MAX,
            CORS_ORIGINS: config.CORS_ORIGINS,
            // Database info (without credentials)
            database: {
                type: 'SQLite',
                path: config.DATABASE_URL ? 'configured' : 'not_configured'
            }
        };
        
        res.json({
            success: true,
            data: sanitizedConfig
        });
        
    } catch (error) {
        apiLogger.error('System config API error', {
            error: error.message
        });
        
        res.status(500).json({
            error: 'Failed to get system configuration',
            message: error.message
        });
    }
});

/**
 * GET /api/system/logs - Get recent system logs
 */
router.get('/logs', [
    query('limit').optional().isInt({ min: 1, max: 1000 }).toInt(),
    query('level').optional().isIn(['error', 'warn', 'info', 'debug']),
    query('module').optional().isString()
], handleValidationErrors, async (req, res) => {
    try {
        const { limit = 100, level, module } = req.query;
        
        // Note: This is a simplified implementation
        // In a production system, you might want to read from log files
        // or use a centralized logging system
        
        const database = req.backend.getDatabase();
        const recentLogs = await database.getRecentScrapeLogs(limit);
        
        res.json({
            success: true,
            data: recentLogs,
            count: recentLogs.length,
            filters: { limit, level, module }
        });
        
    } catch (error) {
        apiLogger.error('System logs API error', {
            error: error.message
        });
        
        res.status(500).json({
            error: 'Failed to get system logs',
            message: error.message
        });
    }
});

/**
 * GET /api/system/metrics - Get system metrics
 */
router.get('/metrics', async (req, res) => {
    try {
        const database = req.backend.getDatabase();
        const statistics = await database.getStatistics();
        
        const metrics = {
            database: statistics,
            system: {
                uptime: process.uptime(),
                memory: process.memoryUsage(),
                cpu: process.cpuUsage()
            },
            services: {
                scraping: {
                    activeJobs: req.backend.getScrapingService()?.activeScrapeJobs?.size || 0,
                    completedJobs: req.backend.getScrapingService()?.scrapeHistory?.length || 0
                },
                processing: {
                    activeJobs: req.backend.getDataProcessingService()?.activeProcessingJobs?.size || 0,
                    completedJobs: req.backend.getDataProcessingService()?.processingHistory?.length || 0
                },
                scheduler: {
                    scheduledJobs: req.backend.getJobSchedulerService()?.scheduledJobs?.size || 0,
                    isRunning: req.backend.getJobSchedulerService()?.isRunning || false
                }
            },
            timestamp: new Date().toISOString()
        };
        
        res.json({
            success: true,
            data: metrics
        });
        
    } catch (error) {
        apiLogger.error('System metrics API error', {
            error: error.message
        });
        
        res.status(500).json({
            error: 'Failed to get system metrics',
            message: error.message
        });
    }
});

/**
 * POST /api/system/restart - Restart backend services (development only)
 */
router.post('/restart', async (req, res) => {
    try {
        if (config.NODE_ENV === 'production') {
            return res.status(403).json({
                error: 'Service restart not allowed in production',
                message: 'Use proper deployment tools for production restarts'
            });
        }
        
        apiLogger.info('System restart requested via API');
        
        // This would typically trigger a graceful restart
        // For now, we'll just return a success message
        res.json({
            success: true,
            message: 'System restart initiated',
            timestamp: new Date().toISOString()
        });
        
        // In a real implementation, you might:
        // setTimeout(() => process.exit(0), 1000);
        
    } catch (error) {
        apiLogger.error('System restart API error', {
            error: error.message
        });
        
        res.status(500).json({
            error: 'Failed to restart system',
            message: error.message
        });
    }
});

export default router;
