/**
 * Scraping API Routes
 * 
 * Handles all scraping-related API endpoints:
 * - Start scraping jobs
 * - Monitor scraping progress
 * - Get scraping results
 * - Cancel scraping jobs
 */

import express from 'express';
import { body, query, validationResult } from 'express-validator';
import { apiLogger } from '../../utils/logger.js';

const router = express.Router();

// Validation error handler
const handleValidationErrors = (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            error: 'Validation error',
            details: errors.array()
        });
    }
    next();
};

/**
 * POST /api/scraping/event - Scrape a single event
 */
router.post('/event', [
    body('eventName').notEmpty().withMessage('Event name is required'),
    body('ageGroup').notEmpty().withMessage('Age group is required'),
    body('gender').isIn(['M', 'W']).withMessage('Gender must be M or W'),
    body('regionId').optional().isInt({ min: 1, max: 18 }).withMessage('Region ID must be between 1 and 18'),
    body('season').optional().isString().withMessage('Season must be a string')
], handleValidationErrors, async (req, res) => {
    try {
        const { eventName, ageGroup, gender, regionId, season } = req.body;
        
        const eventConfig = {
            eventName,
            ageGroup,
            gender,
            regionId: regionId || 1,
            season: season || '2025',
            timeRange: '01.06.2024|31.05.2025'
        };
        
        apiLogger.info('Starting event scraping via API', { eventConfig });
        
        const scrapingService = req.backend.getScrapingService();
        const result = await scrapingService.scrapeEvent(eventConfig);
        
        res.json({
            success: true,
            message: 'Event scraping completed',
            jobId: result.jobId,
            rankingsFound: result.rankingsFound,
            timestamp: new Date().toISOString()
        });
        
    } catch (error) {
        apiLogger.error('Event scraping API error', {
            error: error.message,
            body: req.body
        });
        
        res.status(500).json({
            error: 'Event scraping failed',
            message: error.message
        });
    }
});

/**
 * POST /api/scraping/all - Scrape all configured events
 */
router.post('/all', async (req, res) => {
    try {
        apiLogger.info('Starting full scraping via API');
        
        const scrapingService = req.backend.getScrapingService();
        const result = await scrapingService.scrapeAllEvents();
        
        res.json({
            success: true,
            message: 'Full scraping started',
            jobId: result.jobId,
            stats: result.stats,
            timestamp: new Date().toISOString()
        });
        
    } catch (error) {
        apiLogger.error('Full scraping API error', {
            error: error.message
        });
        
        res.status(500).json({
            error: 'Full scraping failed',
            message: error.message
        });
    }
});

/**
 * GET /api/scraping/status/:jobId - Get scraping job status
 */
router.get('/status/:jobId', async (req, res) => {
    try {
        const { jobId } = req.params;
        
        const scrapingService = req.backend.getScrapingService();
        const status = scrapingService.getJobStatus(jobId);
        
        if (!status) {
            return res.status(404).json({
                error: 'Job not found',
                jobId
            });
        }
        
        res.json({
            success: true,
            jobId,
            status
        });
        
    } catch (error) {
        apiLogger.error('Scraping status API error', {
            error: error.message,
            jobId: req.params.jobId
        });
        
        res.status(500).json({
            error: 'Failed to get scraping status',
            message: error.message
        });
    }
});

/**
 * GET /api/scraping/jobs - Get all active scraping jobs
 */
router.get('/jobs', async (req, res) => {
    try {
        const scrapingService = req.backend.getScrapingService();
        const activeJobs = scrapingService.getActiveJobs();
        
        res.json({
            success: true,
            activeJobs,
            count: activeJobs.length
        });
        
    } catch (error) {
        apiLogger.error('Active jobs API error', {
            error: error.message
        });
        
        res.status(500).json({
            error: 'Failed to get active jobs',
            message: error.message
        });
    }
});

/**
 * GET /api/scraping/history - Get scraping history
 */
router.get('/history', [
    query('limit').optional().isInt({ min: 1, max: 100 }).toInt()
], handleValidationErrors, async (req, res) => {
    try {
        const { limit = 20 } = req.query;
        
        const scrapingService = req.backend.getScrapingService();
        const history = scrapingService.getScrapeHistory(limit);
        
        res.json({
            success: true,
            history,
            count: history.length
        });
        
    } catch (error) {
        apiLogger.error('Scraping history API error', {
            error: error.message
        });
        
        res.status(500).json({
            error: 'Failed to get scraping history',
            message: error.message
        });
    }
});

/**
 * DELETE /api/scraping/jobs/:jobId - Cancel a scraping job
 */
router.delete('/jobs/:jobId', async (req, res) => {
    try {
        const { jobId } = req.params;
        
        const scrapingService = req.backend.getScrapingService();
        const result = await scrapingService.cancelJob(jobId);
        
        res.json({
            success: true,
            message: 'Job cancelled successfully',
            jobId
        });
        
    } catch (error) {
        apiLogger.error('Cancel job API error', {
            error: error.message,
            jobId: req.params.jobId
        });
        
        res.status(500).json({
            error: 'Failed to cancel job',
            message: error.message
        });
    }
});

/**
 * GET /api/scraping/health - Get scraping service health
 */
router.get('/health', async (req, res) => {
    try {
        const scrapingService = req.backend.getScrapingService();
        const health = await scrapingService.healthCheck();
        
        res.json({
            success: true,
            health
        });
        
    } catch (error) {
        apiLogger.error('Scraping health API error', {
            error: error.message
        });
        
        res.status(500).json({
            error: 'Failed to get scraping health',
            message: error.message
        });
    }
});

export default router;
