/**
 * Data API Routes
 * 
 * Handles all data-related API endpoints:
 * - Retrieve rankings
 * - Get events and regions
 * - Export data
 * - Get statistics
 */

import express from 'express';
import { query, validationResult } from 'express-validator';
import { apiLogger } from '../../utils/logger.js';

const router = express.Router();

// Validation error handler
const handleValidationErrors = (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            error: 'Validation error',
            details: errors.array()
        });
    }
    next();
};

/**
 * GET /api/data/rankings - Get rankings with filtering and pagination
 */
router.get('/rankings', [
    query('eventId').optional().isString(),
    query('regionId').optional().isInt({ min: 1, max: 18 }),
    query('ageGroup').optional().isString(),
    query('gender').optional().isIn(['M', 'W']),
    query('season').optional().isString(),
    query('limit').optional().isInt({ min: 1, max: 1000 }).toInt(),
    query('page').optional().isInt({ min: 1 }).toInt()
], handleValidationErrors, async (req, res) => {
    try {
        const {
            eventId,
            regionId,
            ageGroup,
            gender,
            season,
            limit = 100,
            page = 1
        } = req.query;
        
        const filters = {
            ...(eventId && { eventId }),
            ...(regionId && { regionId: parseInt(regionId) }),
            ...(ageGroup && { ageGroup }),
            ...(gender && { gender }),
            ...(season && { season }),
            take: limit,
            skip: (page - 1) * limit
        };
        
        const database = req.backend.getDatabase();
        const rankings = await database.getRankings(filters);
        
        // Get total count for pagination
        const totalCount = await database.prisma.ranking.count({
            where: {
                ...(eventId && { eventId }),
                ...(regionId && { regionId: parseInt(regionId) }),
                ...(ageGroup && { ageGroup }),
                ...(gender && { gender }),
                ...(season && { season })
            }
        });
        
        res.json({
            success: true,
            data: rankings,
            pagination: {
                page,
                limit,
                total: totalCount,
                pages: Math.ceil(totalCount / limit)
            },
            filters: {
                eventId,
                regionId,
                ageGroup,
                gender,
                season
            }
        });
        
    } catch (error) {
        apiLogger.error('Rankings API error', {
            error: error.message,
            query: req.query
        });
        
        res.status(500).json({
            error: 'Failed to retrieve rankings',
            message: error.message
        });
    }
});

/**
 * GET /api/data/rankings/top - Get top rankings
 */
router.get('/rankings/top', [
    query('eventId').optional().isString(),
    query('regionId').optional().isInt({ min: 1, max: 18 }),
    query('ageGroup').optional().isString(),
    query('gender').optional().isIn(['M', 'W']),
    query('limit').optional().isInt({ min: 1, max: 100 }).toInt()
], handleValidationErrors, async (req, res) => {
    try {
        const {
            eventId,
            regionId,
            ageGroup,
            gender,
            limit = 10
        } = req.query;
        
        const filters = {
            ...(eventId && { eventId }),
            ...(regionId && { regionId: parseInt(regionId) }),
            ...(ageGroup && { ageGroup }),
            ...(gender && { gender }),
            take: limit
        };
        
        const database = req.backend.getDatabase();
        const topRankings = await database.getRankings(filters);
        
        res.json({
            success: true,
            data: topRankings,
            count: topRankings.length
        });
        
    } catch (error) {
        apiLogger.error('Top rankings API error', {
            error: error.message,
            query: req.query
        });
        
        res.status(500).json({
            error: 'Failed to retrieve top rankings',
            message: error.message
        });
    }
});

/**
 * GET /api/data/events - Get all events
 */
router.get('/events', async (req, res) => {
    try {
        const database = req.backend.getDatabase();
        const events = await database.getEvents();
        
        res.json({
            success: true,
            data: events,
            count: events.length
        });
        
    } catch (error) {
        apiLogger.error('Events API error', {
            error: error.message
        });
        
        res.status(500).json({
            error: 'Failed to retrieve events',
            message: error.message
        });
    }
});

/**
 * GET /api/data/regions - Get all regions
 */
router.get('/regions', async (req, res) => {
    try {
        const database = req.backend.getDatabase();
        const regions = await database.getRegions();
        
        res.json({
            success: true,
            data: regions,
            count: regions.length
        });
        
    } catch (error) {
        apiLogger.error('Regions API error', {
            error: error.message
        });
        
        res.status(500).json({
            error: 'Failed to retrieve regions',
            message: error.message
        });
    }
});

/**
 * GET /api/data/statistics - Get database statistics
 */
router.get('/statistics', async (req, res) => {
    try {
        const database = req.backend.getDatabase();
        const statistics = await database.getStatistics();
        
        res.json({
            success: true,
            data: statistics
        });
        
    } catch (error) {
        apiLogger.error('Statistics API error', {
            error: error.message
        });
        
        res.status(500).json({
            error: 'Failed to retrieve statistics',
            message: error.message
        });
    }
});

/**
 * GET /api/data/export/csv - Export rankings as CSV
 */
router.get('/export/csv', [
    query('eventId').optional().isString(),
    query('regionId').optional().isInt({ min: 1, max: 18 }),
    query('ageGroup').optional().isString(),
    query('gender').optional().isIn(['M', 'W']),
    query('season').optional().isString()
], handleValidationErrors, async (req, res) => {
    try {
        const {
            eventId,
            regionId,
            ageGroup,
            gender,
            season
        } = req.query;
        
        const filters = {
            ...(eventId && { eventId }),
            ...(regionId && { regionId: parseInt(regionId) }),
            ...(ageGroup && { ageGroup }),
            ...(gender && { gender }),
            ...(season && { season }),
            take: 10000 // Maximum for export
        };
        
        const database = req.backend.getDatabase();
        const rankings = await database.getRankings(filters);
        
        // CSV Header
        const csvHeader = [
            'Rank',
            'Name',
            'Birth Year',
            'Club',
            'Time',
            'Event',
            'Region',
            'Age Group',
            'Gender',
            'Competition',
            'Date',
            'Location'
        ].join(',');
        
        // CSV Data
        const csvData = rankings.map(ranking => [
            ranking.rank,
            `"${ranking.swimmerName}"`,
            ranking.birthYear || '',
            `"${ranking.club}"`,
            ranking.time,
            `"${ranking.event.name}"`,
            `"${ranking.region.name}"`,
            ranking.ageGroup,
            ranking.gender,
            `"${ranking.competition || ''}"`,
            ranking.date ? ranking.date.toISOString().split('T')[0] : '',
            `"${ranking.location || ''}"`
        ].join(',')).join('\n');
        
        const csv = `${csvHeader}\n${csvData}`;
        
        // Generate filename
        const timestamp = new Date().toISOString().split('T')[0];
        const filename = `dsv-rankings-${timestamp}.csv`;
        
        res.setHeader('Content-Type', 'text/csv; charset=utf-8');
        res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
        res.send('\ufeff' + csv); // BOM for correct UTF-8 display in Excel
        
    } catch (error) {
        apiLogger.error('CSV export API error', {
            error: error.message,
            query: req.query
        });
        
        res.status(500).json({
            error: 'Failed to export CSV',
            message: error.message
        });
    }
});

export default router;
