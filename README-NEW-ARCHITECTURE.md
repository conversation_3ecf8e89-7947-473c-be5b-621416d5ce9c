# DSV Scraper - Modular Architecture

A powerful web scraper for Deutsche Schwimm-Verband (DSV) rankings with modern modular Node.js architecture.

## 🏊‍♂️ Overview

This project automatically scrapes swimming rankings from the DSV portal and provides them through a user-friendly web interface. The new modular architecture provides clean separation of concerns, improved scalability, and better maintainability.

## 🏗️ Architecture

The application is built with a modular architecture consisting of three independent layers:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│    Frontend     │    │   API Layer     │    │    Backend      │
│   (Port 3000)   │◄──►│  (Port 3001)    │◄──►│   Services      │
│                 │    │                 │    │                 │
│ • Web Interface │    │ • REST API      │    │ • Scraping      │
│ • Client Logic  │    │ • Validation    │    │ • Data Proc.    │
│ • API Calls     │    │ • Error Handle  │    │ • Database      │
│                 │    │                 │    │ • Job Scheduler │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Components

- **Backend Service** (`src/backend/`): Core business logic, scraping, data processing, job scheduling
- **API Layer** (`src/api/`): REST API interface with validation and error handling  
- **Frontend Service** (`src/frontend/`): Web interface that communicates exclusively through API

## ✨ Features

- **Modular Architecture** with clean separation of concerns
- **Independent Services** that can be scaled and deployed separately
- **REST API** with comprehensive endpoints for all operations
- **Automated Scraping** with configurable scheduling
- **Real-time Job Monitoring** and status tracking
- **Data Export** in multiple formats (CSV, JSON)
- **Health Monitoring** with detailed system status
- **Comprehensive Testing** with unit, integration, and E2E tests
- **Docker Support** for containerized deployment
- **Database Integration** with PostgreSQL/SQLite

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ and npm 9+
- PostgreSQL (optional, SQLite works for development)
- Git

### Installation

```bash
# Clone repository
git clone <repository-url>
cd dsv-scraper

# Install dependencies
npm install

# Set up environment
cp .env.example .env
# Edit .env with your configuration

# Set up database
npm run db:generate
npm run db:migrate

# Start all services
npm run dev:all
```

The application will be available at:
- **Frontend**: http://localhost:3000
- **API**: http://localhost:3001/api
- **Health checks**: http://localhost:3000/health, http://localhost:3001/health

### Individual Service Startup

```bash
# Start services individually
npm run backend:dev    # Backend service
npm run api:dev        # API service  
npm run frontend:dev   # Frontend service
```

### Production Deployment

```bash
# Start all services in production
npm run backend &
npm run api &
npm run frontend &

# Or use PM2 for process management
pm2 start ecosystem.config.js
```

## 📚 Documentation

- **[Architecture Guide](README-ARCHITECTURE.md)** - Detailed architecture overview
- **[API Documentation](docs/API.md)** - Complete API reference
- **[Development Guide](docs/DEVELOPMENT.md)** - Development setup and workflows
- **[Deployment Guide](docs/DEPLOYMENT.md)** - Production deployment options

## 🧪 Testing

```bash
# Run all tests
npm test

# Run specific test suites
npm test -- tests/backend/
npm test -- tests/api/
npm test -- tests/integration/

# Run tests in watch mode
npm run test:watch

# Generate coverage report
npm test -- --coverage
```

## 🔧 Configuration

### Environment Variables

```bash
# Server Configuration
PORT=3000                    # Frontend port
API_PORT=3001               # API port
FRONTEND_PORT=3000          # Frontend port (alternative)

# Database
DATABASE_URL=postgresql://user:pass@localhost:5432/dsv_rankings

# Scheduler
ENABLE_SCHEDULER=true
ENABLE_WEEKLY_SCRAPING=true
ENABLE_DAILY_CLEANUP=true
ENABLE_HEALTH_CHECKS=true

# Logging
LOG_LEVEL=info
NODE_ENV=development
```

## 📊 API Endpoints

### Scraping Operations
- `POST /api/scraping/event` - Scrape single event
- `POST /api/scraping/all` - Scrape all events
- `GET /api/scraping/jobs` - Get active jobs
- `GET /api/scraping/status/:id` - Get job status

### Data Access
- `GET /api/data/rankings` - Get rankings with filters
- `GET /api/data/events` - Get all events
- `GET /api/data/regions` - Get all regions
- `GET /api/data/export/csv` - Export CSV

### Job Management
- `GET /api/jobs/scheduled` - Get scheduled jobs
- `POST /api/jobs/scheduled/:name/execute` - Execute job
- `PUT /api/jobs/scheduled/:name/toggle` - Enable/disable job

### System Monitoring
- `GET /api/system/health` - System health
- `GET /api/system/status` - System status
- `GET /api/system/metrics` - System metrics

## 🐳 Docker Deployment

```bash
# Build and run with Docker Compose
docker-compose up -d

# Scale API service
docker-compose up -d --scale api=3

# View logs
docker-compose logs -f
```

## 📈 Monitoring

Each service provides health check endpoints:
- Frontend: `GET /health`
- API: `GET /health` 
- Backend: Available through API at `GET /api/system/health`

## 🔄 Migration from Old Architecture

The modular architecture replaces the monolithic structure:

- **Old**: Single `src/web/app.js` with mixed concerns
- **New**: Separated into Backend, API, and Frontend services
- **Benefits**: Better scalability, testability, and maintainability

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass (`npm test`)
6. Commit your changes (`git commit -m 'Add amazing feature'`)
7. Push to the branch (`git push origin feature/amazing-feature`)
8. Open a Pull Request

## 📝 Development Workflow

1. **Setup**: Follow the installation guide
2. **Development**: Use `npm run dev:all` for hot reloading
3. **Testing**: Write tests for new features
4. **Code Quality**: Run `npm run lint` and `npm run format`
5. **Documentation**: Update relevant documentation

## 🐛 Troubleshooting

### Common Issues

1. **Port conflicts**: Check if ports 3000/3001 are in use
2. **Database connection**: Verify DATABASE_URL in .env
3. **Service communication**: Ensure all services are running
4. **API errors**: Check backend service health

### Debug Commands

```bash
# Check service status
npm run backend:dev    # Check backend logs
npm run api:dev        # Check API logs
npm run frontend:dev   # Check frontend logs

# Test API connectivity
curl http://localhost:3001/health
curl http://localhost:3000/health
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Deutsche Schwimm-Verband (DSV) for providing the data source
- Node.js and Express.js communities
- All contributors to this project

## 📞 Support

For support and questions:
- Create an issue on GitHub
- Check the documentation in the `docs/` folder
- Review the troubleshooting section above

---

**Note**: This is the new modular architecture. For the legacy monolithic version, see the original README.md file.
