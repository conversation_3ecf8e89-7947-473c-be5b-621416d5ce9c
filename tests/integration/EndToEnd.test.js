/**
 * End-to-End Integration Tests
 * 
 * Tests the complete flow from frontend through API to backend services
 */

import { jest } from '@jest/globals';
import request from 'supertest';
import { createApiServer } from '../../src/api/index.js';
import { createFrontendServer } from '../../src/frontend/index.js';
import BackendService from '../../src/backend/services/BackendService.js';

// Mock external dependencies
jest.mock('../../src/utils/DatabaseManager.js');
jest.mock('../../src/scrapers/DSVScraper.js');

describe('End-to-End Integration Tests', () => {
    let backendService;
    let apiApp;
    let frontendApp;

    beforeAll(async () => {
        // Initialize backend service with mocks
        backendService = new BackendService();
        
        // Mock backend service methods
        backendService.initialize = jest.fn().mockResolvedValue(true);
        backendService.start = jest.fn().mockResolvedValue(true);
        backendService.stop = jest.fn().mockResolvedValue(true);
        backendService.healthCheck = jest.fn().mockResolvedValue({
            status: 'healthy',
            timestamp: new Date().toISOString(),
            uptime: 12345,
            services: {
                database: { status: 'healthy' },
                scraping: { status: 'healthy' },
                dataProcessing: { status: 'healthy' },
                jobScheduler: { status: 'healthy' }
            }
        });

        // Mock service getters
        backendService.getDatabase = jest.fn().mockReturnValue({
            getRankings: jest.fn().mockResolvedValue([
                {
                    id: 1,
                    rank: 1,
                    swimmerName: 'Test Swimmer',
                    time: '00:25.50',
                    event: { name: 'Test Event' },
                    region: { name: 'Test Region' }
                }
            ]),
            getEvents: jest.fn().mockResolvedValue([
                { id: 1, name: 'Test Event', code: 'TEST' }
            ]),
            getRegions: jest.fn().mockResolvedValue([
                { id: 1, name: 'Test Region' }
            ]),
            getStatistics: jest.fn().mockResolvedValue({
                totalRankings: 1000,
                totalEvents: 50,
                totalRegions: 18
            }),
            prisma: {
                ranking: {
                    count: jest.fn().mockResolvedValue(1000)
                }
            }
        });

        backendService.getScrapingService = jest.fn().mockReturnValue({
            scrapeEvent: jest.fn().mockResolvedValue({
                jobId: 'e2e-test-job-123',
                success: true,
                rankingsFound: 25
            }),
            scrapeAllEvents: jest.fn().mockResolvedValue({
                jobId: 'e2e-test-job-456',
                success: true,
                stats: { totalEvents: 10, totalRankings: 500 }
            }),
            getJobStatus: jest.fn().mockReturnValue({
                status: 'completed',
                startTime: new Date(),
                endTime: new Date(),
                results: { rankingsFound: 25 }
            }),
            getActiveJobs: jest.fn().mockReturnValue([]),
            getScrapeHistory: jest.fn().mockReturnValue([
                {
                    jobId: 'e2e-test-job-123',
                    status: 'completed',
                    startTime: new Date(),
                    endTime: new Date()
                }
            ])
        });

        backendService.getDataProcessingService = jest.fn().mockReturnValue({
            processScrapingResults: jest.fn().mockResolvedValue({
                jobId: 'e2e-process-job-789',
                success: true,
                stats: { totalSaved: 25, totalUpdated: 0 }
            }),
            getActiveJobs: jest.fn().mockReturnValue([]),
            getProcessingHistory: jest.fn().mockReturnValue([])
        });

        backendService.getJobSchedulerService = jest.fn().mockReturnValue({
            getScheduledJobs: jest.fn().mockReturnValue([
                {
                    name: 'weekly_scraping',
                    enabled: true,
                    cronExpression: '0 2 * * 1',
                    lastRun: new Date(),
                    runCount: 5
                }
            ]),
            executeJobManually: jest.fn().mockResolvedValue({
                success: true,
                result: { message: 'Job executed successfully' }
            }),
            toggleJob: jest.fn().mockReturnValue(true)
        });

        // Mock the backend service getter
        jest.doMock('../../src/backend/index.js', () => ({
            getBackendService: jest.fn(() => backendService)
        }));

        // Create API and Frontend apps
        apiApp = createApiServer();
        frontendApp = createFrontendServer();
    });

    afterAll(async () => {
        if (backendService) {
            await backendService.stop();
        }
    });

    describe('Complete Scraping Workflow', () => {
        test('should complete full scraping workflow', async () => {
            // 1. Start scraping via API
            const scrapingResponse = await request(apiApp)
                .post('/api/scraping/event')
                .send({
                    eventName: 'Test Event',
                    ageGroup: '2015',
                    gender: 'M',
                    regionId: 1
                })
                .expect(200);

            expect(scrapingResponse.body).toMatchObject({
                success: true,
                jobId: 'e2e-test-job-123',
                rankingsFound: 25
            });

            // 2. Check job status
            const statusResponse = await request(apiApp)
                .get('/api/scraping/status/e2e-test-job-123')
                .expect(200);

            expect(statusResponse.body).toMatchObject({
                success: true,
                jobId: 'e2e-test-job-123',
                status: {
                    status: 'completed'
                }
            });

            // 3. Verify data is available
            const dataResponse = await request(apiApp)
                .get('/api/data/rankings')
                .query({ limit: 10 })
                .expect(200);

            expect(dataResponse.body).toMatchObject({
                success: true,
                data: expect.arrayContaining([
                    expect.objectContaining({
                        swimmerName: 'Test Swimmer'
                    })
                ])
            });
        });

        test('should handle scraping errors gracefully', async () => {
            // Mock scraping failure
            backendService.getScrapingService().scrapeEvent.mockRejectedValueOnce(
                new Error('Scraping service unavailable')
            );

            const response = await request(apiApp)
                .post('/api/scraping/event')
                .send({
                    eventName: 'Test Event',
                    ageGroup: '2015',
                    gender: 'M'
                })
                .expect(500);

            expect(response.body).toMatchObject({
                error: 'Event scraping failed',
                message: 'Scraping service unavailable'
            });
        });
    });

    describe('Data Management Workflow', () => {
        test('should retrieve and export data', async () => {
            // 1. Get rankings
            const rankingsResponse = await request(apiApp)
                .get('/api/data/rankings')
                .query({ eventId: 'TEST', limit: 5 })
                .expect(200);

            expect(rankingsResponse.body.success).toBe(true);

            // 2. Get events
            const eventsResponse = await request(apiApp)
                .get('/api/data/events')
                .expect(200);

            expect(eventsResponse.body).toMatchObject({
                success: true,
                data: expect.arrayContaining([
                    expect.objectContaining({
                        name: 'Test Event'
                    })
                ])
            });

            // 3. Export CSV
            const csvResponse = await request(apiApp)
                .get('/api/data/export/csv')
                .query({ eventId: 'TEST' })
                .expect(200);

            expect(csvResponse.headers['content-type']).toContain('text/csv');
            expect(csvResponse.headers['content-disposition']).toContain('attachment');
        });

        test('should get statistics', async () => {
            const response = await request(apiApp)
                .get('/api/data/statistics')
                .expect(200);

            expect(response.body).toMatchObject({
                success: true,
                data: {
                    totalRankings: 1000,
                    totalEvents: 50,
                    totalRegions: 18
                }
            });
        });
    });

    describe('Job Management Workflow', () => {
        test('should manage scheduled jobs', async () => {
            // 1. Get scheduled jobs
            const jobsResponse = await request(apiApp)
                .get('/api/jobs/scheduled')
                .expect(200);

            expect(jobsResponse.body).toMatchObject({
                success: true,
                data: expect.arrayContaining([
                    expect.objectContaining({
                        name: 'weekly_scraping',
                        enabled: true
                    })
                ])
            });

            // 2. Execute job manually
            const executeResponse = await request(apiApp)
                .post('/api/jobs/scheduled/weekly_scraping/execute')
                .expect(200);

            expect(executeResponse.body).toMatchObject({
                success: true,
                jobName: 'weekly_scraping'
            });

            // 3. Toggle job
            const toggleResponse = await request(apiApp)
                .put('/api/jobs/scheduled/weekly_scraping/toggle')
                .send({ enabled: false })
                .expect(200);

            expect(toggleResponse.body).toMatchObject({
                success: true,
                jobName: 'weekly_scraping',
                enabled: false
            });
        });

        test('should start cleanup job', async () => {
            const response = await request(apiApp)
                .post('/api/jobs/processing/cleanup')
                .send({ olderThanDays: 30 })
                .expect(200);

            expect(response.body).toMatchObject({
                success: true,
                message: 'Cleanup job started'
            });
        });
    });

    describe('System Monitoring Workflow', () => {
        test('should provide comprehensive system health', async () => {
            const response = await request(apiApp)
                .get('/api/system/health')
                .expect(200);

            expect(response.body).toMatchObject({
                status: 'healthy',
                services: {
                    database: { status: 'healthy' },
                    scraping: { status: 'healthy' },
                    dataProcessing: { status: 'healthy' },
                    jobScheduler: { status: 'healthy' }
                },
                api: {
                    status: 'healthy'
                }
            });
        });

        test('should provide system status and metrics', async () => {
            // System status
            const statusResponse = await request(apiApp)
                .get('/api/system/status')
                .expect(200);

            expect(statusResponse.body.success).toBe(true);
            expect(statusResponse.body.data).toHaveProperty('backend');
            expect(statusResponse.body.data).toHaveProperty('services');
            expect(statusResponse.body.data).toHaveProperty('system');

            // System metrics
            const metricsResponse = await request(apiApp)
                .get('/api/system/metrics')
                .expect(200);

            expect(metricsResponse.body.success).toBe(true);
            expect(metricsResponse.body.data).toHaveProperty('database');
            expect(metricsResponse.body.data).toHaveProperty('system');
            expect(metricsResponse.body.data).toHaveProperty('services');
        });
    });

    describe('Frontend Integration', () => {
        test('should serve frontend pages', async () => {
            const response = await request(frontendApp)
                .get('/')
                .expect(200);

            expect(response.text).toContain('DSV Scraper Dashboard');
            expect(response.headers['content-type']).toContain('text/html');
        });

        test('should serve health check', async () => {
            const response = await request(frontendApp)
                .get('/health')
                .expect(200);

            expect(response.body).toMatchObject({
                status: 'healthy',
                service: 'frontend'
            });
        });

        test('should handle 404 errors', async () => {
            const response = await request(frontendApp)
                .get('/nonexistent')
                .expect(404);

            expect(response.text).toContain('Page Not Found');
        });
    });

    describe('Error Recovery', () => {
        test('should handle backend service unavailability', async () => {
            // Mock backend service as unavailable
            jest.doMock('../../src/backend/index.js', () => ({
                getBackendService: jest.fn(() => null)
            }));

            const response = await request(apiApp)
                .get('/api/data/rankings')
                .expect(503);

            expect(response.body).toMatchObject({
                error: 'Backend service not available'
            });
        });

        test('should handle database connection errors', async () => {
            backendService.getDatabase().getRankings.mockRejectedValueOnce(
                new Error('Database connection lost')
            );

            const response = await request(apiApp)
                .get('/api/data/rankings')
                .expect(500);

            expect(response.body).toMatchObject({
                error: 'Failed to retrieve rankings',
                message: 'Database connection lost'
            });
        });
    });

    describe('Performance Tests', () => {
        test('should handle concurrent requests', async () => {
            const concurrentRequests = 10;
            const promises = Array(concurrentRequests).fill().map(() =>
                request(apiApp).get('/api/data/events')
            );

            const responses = await Promise.all(promises);
            
            responses.forEach(response => {
                expect(response.status).toBe(200);
                expect(response.body.success).toBe(true);
            });
        });

        test('should respond within acceptable time limits', async () => {
            const startTime = Date.now();
            
            await request(apiApp)
                .get('/api/data/rankings')
                .expect(200);
            
            const responseTime = Date.now() - startTime;
            expect(responseTime).toBeLessThan(1000); // Should respond within 1 second
        });
    });
});
