/**
 * Tests für Config Module
 */
import { describe, test, expect, beforeEach, afterEach } from '@jest/globals';

describe('Config Module', () => {
    let originalEnv;
    
    beforeEach(() => {
        originalEnv = { ...process.env };
    });
    
    afterEach(() => {
        process.env = originalEnv;
        // Note: ES modules don't have require.cache, module reloading is handled differently
    });
    
    test('should load default configuration', async () => {
        const { default: config } = await import('../src/config.js');
        
        expect(config.NODE_ENV).toBe('test');
        expect(config.PORT).toBe(3000);
        expect(config.DATABASE_TYPE).toBe('sqlite');
        expect(config.SCRAPING_DELAY).toBe(1);
        expect(config.MAX_RETRIES).toBe(3);
    });
    
    test('should override config with environment variables', async () => {
        process.env.PORT = '4000';
        process.env.SCRAPING_DELAY = '5';
        process.env.MAX_RETRIES = '10';
        
        const { default: config } = await import('../src/config.js');
        
        expect(config.PORT).toBe(4000);
        expect(config.SCRAPING_DELAY).toBe(5);
        expect(config.MAX_RETRIES).toBe(10);
    });
    
    test('should validate configuration', async () => {
        process.env.DATABASE_TYPE = 'appwrite';
        process.env.APPWRITE_API_KEY = '';
        
        await expect(async () => {
            await import('../src/config.js');
        }).rejects.toThrow('APPWRITE_API_KEY ist erforderlich');
    });
    
    test('should validate scrape time format', async () => {
        process.env.ENABLE_SCHEDULER = 'true';
        process.env.SCRAPE_TIME = 'invalid-time';
        
        await expect(async () => {
            await import('../src/config.js');
        }).rejects.toThrow('SCRAPE_TIME muss im Format HH:MM sein');
    });
    
    test('should return correct database URL', async () => {
        process.env.DATABASE_TYPE = 'sqlite';
        process.env.SQLITE_URL = 'file:./test.db';
        
        const { default: config } = await import('../src/config.js');
        
        expect(config.getDatabaseUrl()).toBe('file:./test.db');
    });
    
    test('should detect Appwrite enabled state', async () => {
        process.env.DATABASE_TYPE = 'appwrite';
        process.env.APPWRITE_API_KEY = 'test-key';
        
        const { default: config } = await import('../src/config.js');
        
        expect(config.isAppwriteEnabled()).toBe(true);
    });
    
    test('should return Puppeteer configuration', async () => {
        process.env.PUPPETEER_HEADLESS = 'false';
        process.env.PUPPETEER_ARGS = '--no-sandbox,--disable-dev-shm-usage';
        
        const { default: config } = await import('../src/config.js');
        
        const puppeteerConfig = config.getPuppeteerConfig();
        
        expect(puppeteerConfig.headless).toBe(false);
        expect(puppeteerConfig.args).toContain('--no-sandbox');
        expect(puppeteerConfig.args).toContain('--disable-dev-shm-usage');
    });
    
    test('should handle boolean environment variables correctly', async () => {
        process.env.ENABLE_SCHEDULER = 'true';
        process.env.PUPPETEER_HEADLESS = 'false';
        
        const { default: config } = await import('../src/config.js');
        
        expect(config.ENABLE_SCHEDULER).toBe(true);
        expect(config.PUPPETEER_HEADLESS).toBe(false);
    });
    
    test('should set default values for missing environment variables', async () => {
        delete process.env.SCRAPING_DELAY;
        delete process.env.MAX_RETRIES;
        delete process.env.TIMEOUT;
        
        const { default: config } = await import('../src/config.js');
        
        expect(config.SCRAPING_DELAY).toBe(1);
        expect(config.MAX_RETRIES).toBe(3);
        expect(config.TIMEOUT).toBe(30000);
    });
});
