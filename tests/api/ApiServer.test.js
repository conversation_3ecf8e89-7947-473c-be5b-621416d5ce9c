/**
 * API Server Integration Tests
 */

import { jest } from '@jest/globals';
import request from 'supertest';
import { createApiServer } from '../../src/api/index.js';

// Mock backend service
const mockBackendService = {
    healthCheck: jest.fn().mockResolvedValue({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: 12345,
        services: {
            database: { status: 'healthy' },
            scraping: { status: 'healthy' },
            dataProcessing: { status: 'healthy' },
            jobScheduler: { status: 'healthy' }
        }
    }),
    getDatabase: jest.fn().mockReturnValue({
        getRankings: jest.fn().mockResolvedValue([]),
        getEvents: jest.fn().mockResolvedValue([]),
        getRegions: jest.fn().mockResolvedValue([]),
        getStatistics: jest.fn().mockResolvedValue({ totalRankings: 1000 }),
        prisma: {
            ranking: {
                count: jest.fn().mockResolvedValue(1000)
            }
        }
    }),
    getScrapingService: jest.fn().mockReturnValue({
        scrapeEvent: jest.fn().mockResolvedValue({
            jobId: 'test-job-123',
            success: true,
            rankingsFound: 50
        }),
        scrapeAllEvents: jest.fn().mockResolvedValue({
            jobId: 'test-job-456',
            success: true,
            stats: { totalEvents: 10, totalRankings: 500 }
        }),
        getJobStatus: jest.fn().mockReturnValue({
            status: 'completed',
            startTime: new Date(),
            endTime: new Date()
        }),
        getActiveJobs: jest.fn().mockReturnValue([]),
        getScrapeHistory: jest.fn().mockReturnValue([]),
        healthCheck: jest.fn().mockResolvedValue({ status: 'healthy' })
    }),
    getDataProcessingService: jest.fn().mockReturnValue({
        getActiveJobs: jest.fn().mockReturnValue([]),
        getProcessingHistory: jest.fn().mockReturnValue([]),
        cleanOldData: jest.fn().mockResolvedValue({
            jobId: 'cleanup-job-789',
            success: true
        })
    }),
    getJobSchedulerService: jest.fn().mockReturnValue({
        getScheduledJobs: jest.fn().mockReturnValue([]),
        getJobHistory: jest.fn().mockReturnValue([]),
        executeJobManually: jest.fn().mockResolvedValue({ success: true }),
        toggleJob: jest.fn().mockReturnValue(true),
        healthCheck: jest.fn().mockResolvedValue({ status: 'healthy' })
    })
};

// Mock the backend service getter
jest.mock('../../src/backend/index.js', () => ({
    getBackendService: jest.fn(() => mockBackendService)
}));

describe('API Server Integration Tests', () => {
    let app;

    beforeAll(() => {
        app = createApiServer();
    });

    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('Root Endpoints', () => {
        test('GET / should return API information', async () => {
            const response = await request(app)
                .get('/')
                .expect(200);

            expect(response.body).toMatchObject({
                name: 'DSV Scraper API',
                status: 'running',
                endpoints: {
                    scraping: '/api/scraping',
                    data: '/api/data',
                    jobs: '/api/jobs',
                    system: '/api/system'
                }
            });
        });

        test('GET /health should return health status', async () => {
            const response = await request(app)
                .get('/health')
                .expect(200);

            expect(response.body).toMatchObject({
                status: 'healthy',
                api: {
                    status: 'healthy'
                }
            });
        });
    });

    describe('Scraping API Endpoints', () => {
        test('POST /api/scraping/event should start event scraping', async () => {
            const eventData = {
                eventName: 'Test Event',
                ageGroup: '2015',
                gender: 'M',
                regionId: 1
            };

            const response = await request(app)
                .post('/api/scraping/event')
                .send(eventData)
                .expect(200);

            expect(response.body).toMatchObject({
                success: true,
                message: 'Event scraping completed',
                jobId: 'test-job-123',
                rankingsFound: 50
            });

            expect(mockBackendService.getScrapingService().scrapeEvent).toHaveBeenCalledWith(
                expect.objectContaining(eventData)
            );
        });

        test('POST /api/scraping/event should validate required fields', async () => {
            const response = await request(app)
                .post('/api/scraping/event')
                .send({
                    eventName: 'Test Event'
                    // Missing required fields
                })
                .expect(400);

            expect(response.body).toMatchObject({
                error: 'Validation error'
            });
        });

        test('POST /api/scraping/all should start full scraping', async () => {
            const response = await request(app)
                .post('/api/scraping/all')
                .expect(200);

            expect(response.body).toMatchObject({
                success: true,
                message: 'Full scraping started',
                jobId: 'test-job-456'
            });

            expect(mockBackendService.getScrapingService().scrapeAllEvents).toHaveBeenCalled();
        });

        test('GET /api/scraping/jobs should return active jobs', async () => {
            const response = await request(app)
                .get('/api/scraping/jobs')
                .expect(200);

            expect(response.body).toMatchObject({
                success: true,
                activeJobs: [],
                count: 0
            });
        });

        test('GET /api/scraping/status/:jobId should return job status', async () => {
            const response = await request(app)
                .get('/api/scraping/status/test-job-123')
                .expect(200);

            expect(response.body).toMatchObject({
                success: true,
                jobId: 'test-job-123',
                status: expect.objectContaining({
                    status: 'completed'
                })
            });
        });
    });

    describe('Data API Endpoints', () => {
        test('GET /api/data/rankings should return rankings with pagination', async () => {
            const response = await request(app)
                .get('/api/data/rankings')
                .query({ limit: 10, page: 1 })
                .expect(200);

            expect(response.body).toMatchObject({
                success: true,
                data: [],
                pagination: {
                    page: 1,
                    limit: 10,
                    total: 1000,
                    pages: 100
                }
            });
        });

        test('GET /api/data/rankings should validate query parameters', async () => {
            const response = await request(app)
                .get('/api/data/rankings')
                .query({ limit: 'invalid' })
                .expect(400);

            expect(response.body).toMatchObject({
                error: 'Validation error'
            });
        });

        test('GET /api/data/events should return events', async () => {
            const response = await request(app)
                .get('/api/data/events')
                .expect(200);

            expect(response.body).toMatchObject({
                success: true,
                data: [],
                count: 0
            });
        });

        test('GET /api/data/regions should return regions', async () => {
            const response = await request(app)
                .get('/api/data/regions')
                .expect(200);

            expect(response.body).toMatchObject({
                success: true,
                data: [],
                count: 0
            });
        });

        test('GET /api/data/statistics should return statistics', async () => {
            const response = await request(app)
                .get('/api/data/statistics')
                .expect(200);

            expect(response.body).toMatchObject({
                success: true,
                data: { totalRankings: 1000 }
            });
        });
    });

    describe('Jobs API Endpoints', () => {
        test('GET /api/jobs/scheduled should return scheduled jobs', async () => {
            const response = await request(app)
                .get('/api/jobs/scheduled')
                .expect(200);

            expect(response.body).toMatchObject({
                success: true,
                data: [],
                count: 0
            });
        });

        test('POST /api/jobs/scheduled/:jobName/execute should execute job', async () => {
            const response = await request(app)
                .post('/api/jobs/scheduled/test-job/execute')
                .expect(200);

            expect(response.body).toMatchObject({
                success: true,
                message: 'Job executed successfully',
                jobName: 'test-job'
            });
        });

        test('PUT /api/jobs/scheduled/:jobName/toggle should toggle job', async () => {
            const response = await request(app)
                .put('/api/jobs/scheduled/test-job/toggle')
                .send({ enabled: true })
                .expect(200);

            expect(response.body).toMatchObject({
                success: true,
                jobName: 'test-job',
                enabled: true
            });
        });

        test('POST /api/jobs/processing/cleanup should start cleanup', async () => {
            const response = await request(app)
                .post('/api/jobs/processing/cleanup')
                .send({ olderThanDays: 30 })
                .expect(200);

            expect(response.body).toMatchObject({
                success: true,
                message: 'Cleanup job started',
                jobId: 'cleanup-job-789'
            });
        });
    });

    describe('System API Endpoints', () => {
        test('GET /api/system/health should return system health', async () => {
            const response = await request(app)
                .get('/api/system/health')
                .expect(200);

            expect(response.body).toMatchObject({
                status: 'healthy',
                api: {
                    status: 'healthy'
                }
            });
        });

        test('GET /api/system/status should return system status', async () => {
            const response = await request(app)
                .get('/api/system/status')
                .expect(200);

            expect(response.body).toMatchObject({
                success: true,
                data: {
                    backend: expect.any(Object),
                    services: expect.any(Object),
                    system: expect.any(Object)
                }
            });
        });

        test('GET /api/system/config should return sanitized config', async () => {
            const response = await request(app)
                .get('/api/system/config')
                .expect(200);

            expect(response.body).toMatchObject({
                success: true,
                data: expect.objectContaining({
                    NODE_ENV: expect.any(String),
                    PORT: expect.any(Number)
                })
            });

            // Should not contain sensitive data
            expect(response.body.data).not.toHaveProperty('DATABASE_URL');
            expect(response.body.data).not.toHaveProperty('APPWRITE_API_KEY');
        });
    });

    describe('Error Handling', () => {
        test('should handle 404 for unknown endpoints', async () => {
            const response = await request(app)
                .get('/api/unknown')
                .expect(404);

            expect(response.body).toMatchObject({
                error: 'API endpoint not found',
                path: '/api/unknown'
            });
        });

        test('should handle backend service errors', async () => {
            mockBackendService.getScrapingService().scrapeEvent.mockRejectedValue(
                new Error('Scraping failed')
            );

            const response = await request(app)
                .post('/api/scraping/event')
                .send({
                    eventName: 'Test Event',
                    ageGroup: '2015',
                    gender: 'M'
                })
                .expect(500);

            expect(response.body).toMatchObject({
                error: 'Event scraping failed',
                message: 'Scraping failed'
            });
        });
    });

    describe('Rate Limiting', () => {
        test('should apply rate limiting to API requests', async () => {
            // Make multiple requests quickly
            const promises = Array(10).fill().map(() =>
                request(app).get('/api/data/events')
            );

            const responses = await Promise.all(promises);
            
            // All requests should succeed (rate limit is high for tests)
            responses.forEach(response => {
                expect([200, 429]).toContain(response.status);
            });
        });
    });
});
