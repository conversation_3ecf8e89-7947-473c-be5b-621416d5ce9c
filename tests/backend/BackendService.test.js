/**
 * Backend Service Integration Tests
 */

import { jest } from '@jest/globals';
import BackendService from '../../src/backend/services/BackendService.js';
import DatabaseManager from '../../src/utils/DatabaseManager.js';

// Mock dependencies
jest.mock('../../src/utils/DatabaseManager.js');
jest.mock('../../src/backend/services/ScrapingService.js');
jest.mock('../../src/backend/services/DataProcessingService.js');
jest.mock('../../src/backend/services/JobSchedulerService.js');

describe('BackendService Integration Tests', () => {
    let backendService;
    let mockDatabase;

    beforeEach(() => {
        // Reset mocks
        jest.clearAllMocks();
        
        // Create mock database
        mockDatabase = {
            connect: jest.fn().mockResolvedValue(true),
            disconnect: jest.fn().mockResolvedValue(true),
            healthCheck: jest.fn().mockResolvedValue({ status: 'healthy' })
        };
        
        DatabaseManager.mockImplementation(() => mockDatabase);
        
        // Create backend service instance
        backendService = new BackendService();
    });

    afterEach(async () => {
        if (backendService && backendService.isRunning) {
            await backendService.stop();
        }
    });

    describe('Initialization', () => {
        test('should initialize all services successfully', async () => {
            await backendService.initialize();
            
            expect(backendService.isInitialized).toBe(true);
            expect(mockDatabase.connect).toHaveBeenCalled();
            expect(backendService.database).toBeDefined();
            expect(backendService.scrapingService).toBeDefined();
            expect(backendService.dataProcessingService).toBeDefined();
            expect(backendService.jobSchedulerService).toBeDefined();
        });

        test('should handle initialization errors', async () => {
            mockDatabase.connect.mockRejectedValue(new Error('Database connection failed'));
            
            await expect(backendService.initialize()).rejects.toThrow('Database connection failed');
            expect(backendService.isInitialized).toBe(false);
        });
    });

    describe('Service Lifecycle', () => {
        beforeEach(async () => {
            await backendService.initialize();
        });

        test('should start all services', async () => {
            await backendService.start();
            
            expect(backendService.isRunning).toBe(true);
            expect(backendService.startTime).toBeInstanceOf(Date);
        });

        test('should stop all services gracefully', async () => {
            await backendService.start();
            await backendService.stop();
            
            expect(backendService.isRunning).toBe(false);
            expect(mockDatabase.disconnect).toHaveBeenCalled();
        });

        test('should not start without initialization', async () => {
            const uninitializedService = new BackendService();
            
            await expect(uninitializedService.start()).rejects.toThrow(
                'Backend service must be initialized before starting'
            );
        });
    });

    describe('Health Checks', () => {
        beforeEach(async () => {
            await backendService.initialize();
            await backendService.start();
        });

        test('should return healthy status when all services are running', async () => {
            const health = await backendService.healthCheck();
            
            expect(health.status).toBe('healthy');
            expect(health.timestamp).toBeDefined();
            expect(health.uptime).toBeGreaterThanOrEqual(0);
            expect(health.services).toBeDefined();
            expect(health.services.database).toBeDefined();
        });

        test('should handle health check errors', async () => {
            mockDatabase.healthCheck.mockRejectedValue(new Error('Health check failed'));
            
            const health = await backendService.healthCheck();
            
            expect(health.status).toBe('error');
            expect(health.error).toBe('Health check failed');
        });
    });

    describe('Service Access', () => {
        beforeEach(async () => {
            await backendService.initialize();
        });

        test('should provide access to all services', () => {
            expect(backendService.getDatabase()).toBe(backendService.database);
            expect(backendService.getScrapingService()).toBe(backendService.scrapingService);
            expect(backendService.getDataProcessingService()).toBe(backendService.dataProcessingService);
            expect(backendService.getJobSchedulerService()).toBe(backendService.jobSchedulerService);
        });

        test('should return service statistics', () => {
            const stats = backendService.getStats();
            
            expect(stats.isInitialized).toBe(true);
            expect(stats.isRunning).toBe(false);
            expect(stats.startTime).toBeNull();
            expect(stats.uptime).toBe(0);
        });
    });

    describe('Error Handling', () => {
        test('should handle service initialization failures gracefully', async () => {
            // Mock scraping service initialization failure
            const ScrapingService = (await import('../../src/backend/services/ScrapingService.js')).default;
            ScrapingService.mockImplementation(() => ({
                initialize: jest.fn().mockRejectedValue(new Error('Scraping service failed'))
            }));
            
            await expect(backendService.initialize()).rejects.toThrow('Scraping service failed');
        });

        test('should handle service stop failures gracefully', async () => {
            await backendService.initialize();
            await backendService.start();
            
            // Mock database disconnect failure
            mockDatabase.disconnect.mockRejectedValue(new Error('Disconnect failed'));
            
            await expect(backendService.stop()).rejects.toThrow('Disconnect failed');
        });
    });

    describe('Concurrent Operations', () => {
        test('should handle multiple initialization attempts', async () => {
            const promises = [
                backendService.initialize(),
                backendService.initialize(),
                backendService.initialize()
            ];
            
            await Promise.all(promises);
            
            expect(backendService.isInitialized).toBe(true);
            expect(mockDatabase.connect).toHaveBeenCalledTimes(3); // Each call attempts connection
        });

        test('should handle start/stop cycles', async () => {
            await backendService.initialize();
            
            // Multiple start/stop cycles
            for (let i = 0; i < 3; i++) {
                await backendService.start();
                expect(backendService.isRunning).toBe(true);
                
                await backendService.stop();
                expect(backendService.isRunning).toBe(false);
            }
        });
    });

    describe('Memory Management', () => {
        test('should clean up resources on stop', async () => {
            await backendService.initialize();
            await backendService.start();
            
            const initialMemory = process.memoryUsage();
            
            await backendService.stop();
            
            // Verify cleanup
            expect(mockDatabase.disconnect).toHaveBeenCalled();
            expect(backendService.isRunning).toBe(false);
            
            // Memory usage should not increase significantly
            const finalMemory = process.memoryUsage();
            const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;
            expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024); // Less than 10MB increase
        });
    });
});
