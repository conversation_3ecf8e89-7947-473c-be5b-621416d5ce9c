/**
 * Test that verifies the new modular architecture can be imported correctly
 */
import { describe, test, expect } from '@jest/globals';

describe('Modular Architecture - Module Imports', () => {
    test('should import backend services successfully', async () => {
        // Test that all backend services can be imported
        const backendIndex = await import('../../src/backend/index.js');
        expect(backendIndex).toBeDefined();
        
        const BackendService = await import('../../src/backend/services/BackendService.js');
        expect(BackendService.default).toBeDefined();
        
        const ScrapingService = await import('../../src/backend/services/ScrapingService.js');
        expect(ScrapingService.default).toBeDefined();
        
        const DataProcessingService = await import('../../src/backend/services/DataProcessingService.js');
        expect(DataProcessingService.default).toBeDefined();
        
        const JobSchedulerService = await import('../../src/backend/services/JobSchedulerService.js');
        expect(JobSchedulerService.default).toBeDefined();
    });
    
    test('should import API services successfully', async () => {
        // Test that API server and routes can be imported
        const apiIndex = await import('../../src/api/index.js');
        expect(apiIndex).toBeDefined();
        
        const scrapingRoutes = await import('../../src/api/routes/scraping.js');
        expect(scrapingRoutes.default).toBeDefined();
        
        const dataRoutes = await import('../../src/api/routes/data.js');
        expect(dataRoutes.default).toBeDefined();
        
        const jobsRoutes = await import('../../src/api/routes/jobs.js');
        expect(jobsRoutes.default).toBeDefined();
        
        const systemRoutes = await import('../../src/api/routes/system.js');
        expect(systemRoutes.default).toBeDefined();
    });
    
    test('should import frontend service successfully', async () => {
        // Test that frontend server can be imported
        const frontendIndex = await import('../../src/frontend/index.js');
        expect(frontendIndex).toBeDefined();
    });
    
    test('should import core utilities successfully', async () => {
        // Test that core utilities still work
        const config = await import('../../src/config.js');
        expect(config.default).toBeDefined();
        expect(config.default.NODE_ENV).toBeDefined();
        
        const logger = await import('../../src/utils/logger.js');
        expect(logger.scraperLogger).toBeDefined();
        expect(logger.databaseLogger).toBeDefined();
        expect(logger.webLogger).toBeDefined();
        
        const DatabaseManager = await import('../../src/utils/DatabaseManager.js');
        expect(DatabaseManager.default).toBeDefined();
        
        const DataProcessor = await import('../../src/utils/DataProcessor.js');
        expect(DataProcessor.DataProcessor).toBeDefined();
    });
    
    test('should import scrapers successfully', async () => {
        // Test that scrapers still work
        const BaseScraper = await import('../../src/scrapers/BaseScraper.js');
        expect(BaseScraper.BaseScraper).toBeDefined();
        expect(BaseScraper.ScrapingConfig).toBeDefined();
        expect(BaseScraper.SwimmerResult).toBeDefined();
        
        const DSVScraper = await import('../../src/scrapers/DSVScraper.js');
        expect(DSVScraper.DSVScraper).toBeDefined();
    });
    
    test('should verify service class constructors', async () => {
        // Test that service classes are proper constructors (some require dependencies)
        const { default: BackendService } = await import('../../src/backend/services/BackendService.js');
        expect(BackendService).toBeInstanceOf(Function);
        expect(BackendService.name).toBe('BackendService');

        const { default: ScrapingService } = await import('../../src/backend/services/ScrapingService.js');
        expect(ScrapingService).toBeInstanceOf(Function);
        expect(ScrapingService.name).toBe('ScrapingService');

        const { default: DataProcessingService } = await import('../../src/backend/services/DataProcessingService.js');
        expect(DataProcessingService).toBeInstanceOf(Function);
        expect(DataProcessingService.name).toBe('DataProcessingService');

        const { default: JobSchedulerService } = await import('../../src/backend/services/JobSchedulerService.js');
        expect(JobSchedulerService).toBeInstanceOf(Function);
        expect(JobSchedulerService.name).toBe('JobSchedulerService');
    });
    
    test('should verify configuration structure', async () => {
        // Test that configuration has expected structure for modular architecture
        const { default: config } = await import('../../src/config.js');

        // Main port (legacy)
        expect(config.PORT).toBeDefined();
        expect(typeof config.PORT).toBe('number');

        // API service port
        expect(config.API_PORT).toBeDefined();
        expect(typeof config.API_PORT).toBe('number');

        // Frontend service port
        expect(config.FRONTEND_PORT).toBeDefined();
        expect(typeof config.FRONTEND_PORT).toBe('number');

        // Ensure API port is different from frontend port
        expect(config.API_PORT).not.toBe(config.FRONTEND_PORT);

        // Verify other essential configuration
        expect(config.NODE_ENV).toBeDefined();
        expect(config.DATABASE_TYPE).toBeDefined();
        expect(config.SCRAPING_DELAY).toBeDefined();
        expect(config.MAX_RETRIES).toBeDefined();
    });
});
