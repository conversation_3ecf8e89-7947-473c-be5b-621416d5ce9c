# DSV Scraper - New Modular Architecture

This document describes the new modular architecture of the DSV Scraper application, which provides clean separation of concerns between backend logic, API layer, and frontend interface.

## Architecture Overview

The application is now structured into three independent layers:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│    Frontend     │    │   API Layer     │    │    Backend      │
│   (Port 3000)   │◄──►│  (Port 3001)    │◄──►│   Services      │
│                 │    │                 │    │                 │
│ • Web Interface │    │ • REST API      │    │ • Scraping      │
│ • Client-side   │    │ • Request       │    │ • Data Proc.    │
│   Logic         │    │   Validation    │    │ • Database      │
│ • API Calls     │    │ • Error         │    │ • Job Scheduler │
│                 │    │   Handling      │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Components

### 1. Backend Service (`src/backend/`)

The backend service handles all core business logic and can run independently:

- **ScrapingService**: Manages all scraping operations
- **DataProcessingService**: Handles data validation, cleaning, and storage
- **JobSchedulerService**: Manages scheduled tasks and cron jobs
- **DatabaseManager**: Handles database connections and operations

**Entry Point**: `src/backend/index.js`

### 2. API Layer (`src/api/`)

The API layer provides a REST interface between frontend and backend:

- **Scraping Routes** (`/api/scraping`): Start/monitor scraping jobs
- **Data Routes** (`/api/data`): Retrieve rankings, events, regions
- **Jobs Routes** (`/api/jobs`): Manage scheduled jobs and processing
- **System Routes** (`/api/system`): Health checks and system status

**Entry Point**: `src/api/index.js`

### 3. Frontend Service (`src/frontend/`)

The frontend service serves the web interface and communicates only through API:

- **Web Interface**: EJS templates with Bootstrap UI
- **API Client**: JavaScript client for API communication
- **Proxy Middleware**: Forwards API requests to backend
- **Static Assets**: CSS, JavaScript, images

**Entry Point**: `src/frontend/index.js`

## Running the Application

### Development Mode

Run all services simultaneously:
```bash
npm run dev:all
```

Or run services individually:
```bash
# Backend service
npm run backend:dev

# API service
npm run api:dev

# Frontend service
npm run frontend:dev
```

### Production Mode

Run services individually:
```bash
# Backend service
npm run backend

# API service
npm run api

# Frontend service
npm run frontend
```

## Configuration

### Environment Variables

```bash
# Server Ports
PORT=3000                    # Default port (used by frontend)
API_PORT=3001               # API service port
FRONTEND_PORT=3000          # Frontend service port

# Database
DATABASE_URL=postgresql://user:pass@localhost:5432/dsv_rankings

# Scheduler
ENABLE_SCHEDULER=true
ENABLE_WEEKLY_SCRAPING=true
ENABLE_DAILY_CLEANUP=true
ENABLE_HEALTH_CHECKS=true

# Logging
LOG_LEVEL=info
NODE_ENV=development
```

### Service Dependencies

- **Frontend** → **API** (via HTTP proxy)
- **API** → **Backend** (direct service calls)
- **Backend** → **Database** (Prisma ORM)

## API Endpoints

### Scraping Operations
- `POST /api/scraping/event` - Scrape single event
- `POST /api/scraping/all` - Scrape all events
- `GET /api/scraping/jobs` - Get active scraping jobs
- `GET /api/scraping/history` - Get scraping history
- `DELETE /api/scraping/jobs/:id` - Cancel scraping job

### Data Access
- `GET /api/data/rankings` - Get rankings with filters
- `GET /api/data/events` - Get all events
- `GET /api/data/regions` - Get all regions
- `GET /api/data/statistics` - Get database statistics
- `GET /api/data/export/csv` - Export rankings as CSV

### Job Management
- `GET /api/jobs/scheduled` - Get scheduled jobs
- `POST /api/jobs/scheduled/:name/execute` - Execute job manually
- `PUT /api/jobs/scheduled/:name/toggle` - Enable/disable job
- `GET /api/jobs/history` - Get job execution history

### System Monitoring
- `GET /api/system/health` - System health check
- `GET /api/system/status` - Detailed system status
- `GET /api/system/config` - System configuration
- `GET /api/system/metrics` - System metrics

## Benefits of New Architecture

### 1. **Separation of Concerns**
- Each layer has a single responsibility
- Changes in one layer don't affect others
- Easier to maintain and debug

### 2. **Scalability**
- Services can be scaled independently
- Backend can handle multiple API instances
- Frontend can be served from CDN

### 3. **Testability**
- Each service can be tested in isolation
- API endpoints can be tested independently
- Mock services for development

### 4. **Deployment Flexibility**
- Services can be deployed on different servers
- Container-friendly architecture
- Load balancing capabilities

### 5. **Development Efficiency**
- Teams can work on different layers simultaneously
- Clear API contracts between layers
- Hot reloading for each service

## Migration from Old Architecture

The old monolithic structure has been refactored:

- `src/web/app.js` → Split into API and Frontend services
- `src/web/routes/api.js` → Moved to `src/api/routes/`
- `src/scrapers/` → Wrapped in `ScrapingService`
- `src/utils/DataProcessor.js` → Wrapped in `DataProcessingService`
- `src/scheduler/` → Wrapped in `JobSchedulerService`

## Testing

Run tests for the entire application:
```bash
npm test
```

Run tests in watch mode:
```bash
npm run test:watch
```

## Monitoring and Logging

Each service has its own logger module:
- `backendLogger` - Backend service operations
- `apiLogger` - API requests and responses
- `frontendLogger` - Frontend requests and errors

Logs are written to:
- Console (development)
- File rotation (production)
- Centralized logging (optional)

## Health Checks

Each service provides health check endpoints:
- Frontend: `GET /health`
- API: `GET /health`
- Backend: Available through API at `GET /api/system/health`

## Next Steps

1. **Container Deployment**: Create Docker containers for each service
2. **Load Balancing**: Set up load balancers for API and Frontend
3. **Monitoring**: Implement Prometheus metrics and Grafana dashboards
4. **CI/CD**: Set up automated testing and deployment pipelines
5. **Documentation**: Generate API documentation with OpenAPI/Swagger
