# DSV Scraper Deployment Guide

This guide covers deployment options for the DSV Scraper modular architecture.

## Architecture Overview

The application consists of three independent services:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│    Frontend     │    │   API Layer     │    │    Backend      │
│   (Port 3000)   │◄──►│  (Port 3001)    │◄──►│   Services      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Prerequisites

- Node.js 18+ and npm 9+
- PostgreSQL or SQLite database
- Git

## Environment Configuration

Create environment files for each deployment stage:

### Development (.env)
```bash
NODE_ENV=development
PORT=3000
API_PORT=3001
FRONTEND_PORT=3000

# Database
DATABASE_URL=postgresql://user:password@localhost:5432/dsv_rankings

# Scheduler
ENABLE_SCHEDULER=true
ENABLE_WEEKLY_SCRAPING=true
ENABLE_DAILY_CLEANUP=true
ENABLE_HEALTH_CHECKS=true

# Logging
LOG_LEVEL=info
```

### Production (.env.production)
```bash
NODE_ENV=production
PORT=3000
API_PORT=3001
FRONTEND_PORT=3000

# Database
DATABASE_URL=*****************************************/dsv_rankings

# Security
CORS_ORIGINS=https://yourdomain.com
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=1000

# Scheduler
ENABLE_SCHEDULER=true
ENABLE_WEEKLY_SCRAPING=true
ENABLE_DAILY_CLEANUP=true
ENABLE_HEALTH_CHECKS=true

# Logging
LOG_LEVEL=warn
LOG_FILE=/var/log/dsv-scraper/app.log

# Monitoring
SENTRY_DSN=your-sentry-dsn
```

## Deployment Options

### 1. Single Server Deployment

Deploy all services on one server with process management.

#### Using PM2

1. **Install PM2:**
```bash
npm install -g pm2
```

2. **Create ecosystem file (ecosystem.config.js):**
```javascript
module.exports = {
  apps: [
    {
      name: 'dsv-backend',
      script: 'src/backend/index.js',
      env: {
        NODE_ENV: 'production',
        PORT: 3002
      },
      instances: 1,
      exec_mode: 'fork'
    },
    {
      name: 'dsv-api',
      script: 'src/api/index.js',
      env: {
        NODE_ENV: 'production',
        API_PORT: 3001
      },
      instances: 2,
      exec_mode: 'cluster'
    },
    {
      name: 'dsv-frontend',
      script: 'src/frontend/index.js',
      env: {
        NODE_ENV: 'production',
        FRONTEND_PORT: 3000,
        API_PORT: 3001
      },
      instances: 2,
      exec_mode: 'cluster'
    }
  ]
};
```

3. **Deploy:**
```bash
# Install dependencies
npm ci --production

# Generate Prisma client
npm run db:generate

# Run database migrations
npm run db:migrate

# Start services
pm2 start ecosystem.config.js

# Save PM2 configuration
pm2 save
pm2 startup
```

### 2. Docker Deployment

#### Multi-stage Dockerfile

Create `Dockerfile`:
```dockerfile
# Build stage
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

# Runtime stage
FROM node:18-alpine AS runtime

WORKDIR /app

# Install dumb-init for proper signal handling
RUN apk add --no-cache dumb-init

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S dsv -u 1001

# Copy application files
COPY --from=builder /app/node_modules ./node_modules
COPY . .

# Generate Prisma client
RUN npx prisma generate

# Set ownership
RUN chown -R dsv:nodejs /app
USER dsv

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:3000/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

EXPOSE 3000

ENTRYPOINT ["dumb-init", "--"]
```

#### Docker Compose

Create `docker-compose.yml`:
```yaml
version: '3.8'

services:
  database:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: dsv_rankings
      POSTGRES_USER: dsv_user
      POSTGRES_PASSWORD: dsv_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U dsv_user -d dsv_rankings"]
      interval: 30s
      timeout: 10s
      retries: 3

  backend:
    build: .
    command: ["node", "src/backend/index.js"]
    environment:
      NODE_ENV: production
      DATABASE_URL: ************************************************/dsv_rankings
      ENABLE_SCHEDULER: "true"
    depends_on:
      database:
        condition: service_healthy
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped

  api:
    build: .
    command: ["node", "src/api/index.js"]
    environment:
      NODE_ENV: production
      API_PORT: 3001
      DATABASE_URL: ************************************************/dsv_rankings
    ports:
      - "3001:3001"
    depends_on:
      - backend
      - database
    restart: unless-stopped

  frontend:
    build: .
    command: ["node", "src/frontend/index.js"]
    environment:
      NODE_ENV: production
      FRONTEND_PORT: 3000
      API_PORT: 3001
    ports:
      - "3000:3000"
    depends_on:
      - api
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - frontend
      - api
    restart: unless-stopped

volumes:
  postgres_data:
```

#### Deploy with Docker Compose

```bash
# Build and start services
docker-compose up -d

# View logs
docker-compose logs -f

# Scale API service
docker-compose up -d --scale api=3

# Update services
docker-compose pull
docker-compose up -d
```

### 3. Kubernetes Deployment

#### Namespace
```yaml
apiVersion: v1
kind: Namespace
metadata:
  name: dsv-scraper
```

#### ConfigMap
```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: dsv-config
  namespace: dsv-scraper
data:
  NODE_ENV: "production"
  API_PORT: "3001"
  FRONTEND_PORT: "3000"
  ENABLE_SCHEDULER: "true"
```

#### Secret
```yaml
apiVersion: v1
kind: Secret
metadata:
  name: dsv-secrets
  namespace: dsv-scraper
type: Opaque
stringData:
  DATABASE_URL: "****************************************/dsv_rankings"
```

#### Backend Deployment
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: dsv-backend
  namespace: dsv-scraper
spec:
  replicas: 1
  selector:
    matchLabels:
      app: dsv-backend
  template:
    metadata:
      labels:
        app: dsv-backend
    spec:
      containers:
      - name: backend
        image: dsv-scraper:latest
        command: ["node", "src/backend/index.js"]
        envFrom:
        - configMapRef:
            name: dsv-config
        - secretRef:
            name: dsv-secrets
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
```

#### API Service
```yaml
apiVersion: v1
kind: Service
metadata:
  name: dsv-api
  namespace: dsv-scraper
spec:
  selector:
    app: dsv-api
  ports:
  - port: 3001
    targetPort: 3001
  type: ClusterIP
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: dsv-api
  namespace: dsv-scraper
spec:
  replicas: 3
  selector:
    matchLabels:
      app: dsv-api
  template:
    metadata:
      labels:
        app: dsv-api
    spec:
      containers:
      - name: api
        image: dsv-scraper:latest
        command: ["node", "src/api/index.js"]
        ports:
        - containerPort: 3001
        envFrom:
        - configMapRef:
            name: dsv-config
        - secretRef:
            name: dsv-secrets
        livenessProbe:
          httpGet:
            path: /health
            port: 3001
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 3001
          initialDelaySeconds: 5
          periodSeconds: 5
```

#### Ingress
```yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: dsv-ingress
  namespace: dsv-scraper
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
spec:
  rules:
  - host: dsv-scraper.yourdomain.com
    http:
      paths:
      - path: /api
        pathType: Prefix
        backend:
          service:
            name: dsv-api
            port:
              number: 3001
      - path: /
        pathType: Prefix
        backend:
          service:
            name: dsv-frontend
            port:
              number: 3000
```

## Monitoring and Logging

### Health Checks

Each service provides health check endpoints:
- Frontend: `GET /health`
- API: `GET /health`
- Backend: Available through API at `GET /api/system/health`

### Logging

Configure centralized logging:

```bash
# Using journalctl (systemd)
journalctl -u dsv-scraper -f

# Using Docker logs
docker-compose logs -f

# Using Kubernetes
kubectl logs -f deployment/dsv-api -n dsv-scraper
```

### Monitoring with Prometheus

Add monitoring endpoints to each service and configure Prometheus scraping.

## Backup and Recovery

### Database Backup

```bash
# PostgreSQL backup
pg_dump -h localhost -U dsv_user dsv_rankings > backup.sql

# Restore
psql -h localhost -U dsv_user dsv_rankings < backup.sql
```

### Application Data

```bash
# Backup logs and configuration
tar -czf dsv-backup-$(date +%Y%m%d).tar.gz logs/ .env*
```

## Security Considerations

1. **Environment Variables**: Never commit sensitive data to version control
2. **Database**: Use strong passwords and connection encryption
3. **Network**: Configure firewalls and use HTTPS in production
4. **Updates**: Regularly update dependencies and base images
5. **Monitoring**: Set up alerts for security events

## Troubleshooting

### Common Issues

1. **Service won't start**: Check environment variables and database connectivity
2. **High memory usage**: Monitor scraping jobs and implement cleanup
3. **Database connection errors**: Verify connection string and network access
4. **API timeouts**: Check backend service health and database performance

### Debug Commands

```bash
# Check service status
pm2 status
docker-compose ps
kubectl get pods -n dsv-scraper

# View logs
pm2 logs dsv-api
docker-compose logs api
kubectl logs deployment/dsv-api -n dsv-scraper

# Test connectivity
curl http://localhost:3001/health
curl http://localhost:3000/health
```
