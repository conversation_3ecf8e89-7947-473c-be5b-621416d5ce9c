# DSV Scraper Development Guide

This guide covers development setup, workflows, and best practices for the DSV Scraper modular architecture.

## Development Setup

### Prerequisites

- Node.js 18+ and npm 9+
- Git
- PostgreSQL (optional, SQLite works for development)
- VS Code (recommended) with extensions:
  - ESLint
  - Prettier
  - Jest
  - Thunder Client (for API testing)

### Initial Setup

1. **Clone the repository:**
```bash
git clone <repository-url>
cd dsv-scraper
```

2. **Install dependencies:**
```bash
npm install
```

3. **Set up environment:**
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. **Set up database:**
```bash
# Generate Prisma client
npm run db:generate

# Run migrations
npm run db:migrate

# Seed database (optional)
npm run db:seed
```

5. **Start development servers:**
```bash
# Start all services
npm run dev:all

# Or start individually
npm run backend:dev    # Backend service (port varies)
npm run api:dev        # API service (port 3001)
npm run frontend:dev   # Frontend service (port 3000)
```

## Project Structure

```
dsv-scraper/
├── src/
│   ├── backend/           # Backend services
│   │   ├── index.js       # Backend entry point
│   │   └── services/      # Service implementations
│   │       ├── BackendService.js
│   │       ├── ScrapingService.js
│   │       ├── DataProcessingService.js
│   │       └── JobSchedulerService.js
│   ├── api/               # REST API layer
│   │   ├── index.js       # API server entry point
│   │   └── routes/        # API route handlers
│   │       ├── scraping.js
│   │       ├── data.js
│   │       ├── jobs.js
│   │       └── system.js
│   ├── frontend/          # Frontend service
│   │   ├── index.js       # Frontend server entry point
│   │   ├── views/         # EJS templates
│   │   └── public/        # Static assets
│   ├── scrapers/          # Scraping logic (legacy)
│   ├── utils/             # Shared utilities
│   └── config.js          # Configuration management
├── tests/                 # Test files
│   ├── backend/           # Backend tests
│   ├── api/               # API tests
│   ├── integration/       # Integration tests
│   └── setup.js           # Test setup
├── docs/                  # Documentation
├── prisma/                # Database schema and migrations
└── logs/                  # Log files
```

## Development Workflow

### 1. Feature Development

1. **Create feature branch:**
```bash
git checkout -b feature/your-feature-name
```

2. **Develop and test:**
```bash
# Run tests
npm test

# Run specific test suite
npm test -- tests/backend/
npm test -- tests/api/

# Run tests in watch mode
npm run test:watch
```

3. **Code quality checks:**
```bash
# Lint code
npm run lint

# Fix linting issues
npm run lint:fix

# Format code
npm run format
```

4. **Commit changes:**
```bash
git add .
git commit -m "feat: add new feature description"
```

### 2. Testing Strategy

#### Unit Tests
Test individual components in isolation:
```javascript
// Example: Testing a service method
import ScrapingService from '../src/backend/services/ScrapingService.js';

describe('ScrapingService', () => {
  test('should scrape event successfully', async () => {
    const service = new ScrapingService(mockDatabase);
    await service.initialize();
    
    const result = await service.scrapeEvent(mockConfig);
    
    expect(result.success).toBe(true);
    expect(result.jobId).toBeDefined();
  });
});
```

#### Integration Tests
Test service interactions:
```javascript
// Example: Testing API endpoints
import request from 'supertest';
import { createApiServer } from '../src/api/index.js';

describe('API Integration', () => {
  test('should start scraping via API', async () => {
    const app = createApiServer();
    
    const response = await request(app)
      .post('/api/scraping/event')
      .send({ eventName: 'Test', ageGroup: '2015', gender: 'M' })
      .expect(200);
    
    expect(response.body.success).toBe(true);
  });
});
```

#### End-to-End Tests
Test complete workflows:
```javascript
// Example: Complete scraping workflow
describe('E2E Scraping Workflow', () => {
  test('should complete full scraping workflow', async () => {
    // 1. Start scraping
    const scrapingResponse = await startScraping();
    
    // 2. Wait for completion
    await waitForJobCompletion(scrapingResponse.jobId);
    
    // 3. Verify data
    const data = await getRankings();
    expect(data.length).toBeGreaterThan(0);
  });
});
```

### 3. Database Development

#### Schema Changes

1. **Create migration:**
```bash
npx prisma migrate dev --name add-new-field
```

2. **Update schema:**
```prisma
// prisma/schema.prisma
model Ranking {
  id          Int      @id @default(autoincrement())
  newField    String?  // Add new field
  // ... existing fields
}
```

3. **Generate client:**
```bash
npm run db:generate
```

#### Seeding Data

```javascript
// prisma/seed.js
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  // Create test data
  await prisma.event.createMany({
    data: [
      { name: '50m Freestyle', code: '50FR' },
      { name: '100m Freestyle', code: '100FR' }
    ]
  });
}

main().catch(console.error);
```

### 4. API Development

#### Adding New Endpoints

1. **Create route handler:**
```javascript
// src/api/routes/newFeature.js
import express from 'express';

const router = express.Router();

router.get('/new-endpoint', async (req, res) => {
  try {
    const result = await req.backend.getNewService().doSomething();
    res.json({ success: true, data: result });
  } catch (error) {
    res.status(500).json({ error: 'Failed', message: error.message });
  }
});

export default router;
```

2. **Register route:**
```javascript
// src/api/index.js
import newFeatureRoutes from './routes/newFeature.js';

app.use('/api/new-feature', newFeatureRoutes);
```

3. **Add tests:**
```javascript
// tests/api/newFeature.test.js
describe('New Feature API', () => {
  test('should handle new endpoint', async () => {
    const response = await request(app)
      .get('/api/new-feature/new-endpoint')
      .expect(200);
    
    expect(response.body.success).toBe(true);
  });
});
```

### 5. Frontend Development

#### Adding New Views

1. **Create EJS template:**
```html
<!-- src/frontend/views/newPage.ejs -->
<%- include('layout', { 
    title: 'New Page',
    apiEndpoint: apiEndpoint,
    body: `
        <h1>New Page</h1>
        <div id="content"></div>
    `,
    pageScript: `
        <script>
            // Page-specific JavaScript
            async function loadData() {
                const data = await api.get('/new-feature/data');
                document.getElementById('content').innerHTML = JSON.stringify(data);
            }
            
            document.addEventListener('DOMContentLoaded', loadData);
        </script>
    `
}) %>
```

2. **Add route:**
```javascript
// src/frontend/index.js
router.get('/new-page', (req, res) => {
  res.render('newPage', {
    title: 'New Page',
    apiEndpoint: '/api'
  });
});
```

## Best Practices

### 1. Code Style

- Use ESLint and Prettier for consistent formatting
- Follow JavaScript ES6+ conventions
- Use async/await instead of callbacks
- Implement proper error handling

### 2. Error Handling

```javascript
// Good: Proper error handling
async function handleRequest(req, res) {
  try {
    const result = await service.doSomething();
    res.json({ success: true, data: result });
  } catch (error) {
    logger.error('Request failed', { error: error.message, stack: error.stack });
    res.status(500).json({ 
      error: 'Operation failed', 
      message: error.message 
    });
  }
}
```

### 3. Logging

```javascript
// Use structured logging
import { apiLogger } from '../utils/logger.js';

apiLogger.info('Processing request', {
  method: req.method,
  url: req.url,
  userId: req.user?.id
});

apiLogger.error('Request failed', {
  error: error.message,
  stack: error.stack,
  requestId: req.id
});
```

### 4. Configuration

```javascript
// Use environment-based configuration
const config = {
  port: process.env.API_PORT || 3001,
  database: {
    url: process.env.DATABASE_URL || 'sqlite:./dev.db'
  },
  features: {
    enableScheduler: process.env.ENABLE_SCHEDULER === 'true'
  }
};
```

### 5. Testing

- Write tests for all new features
- Maintain high test coverage (>80%)
- Use descriptive test names
- Mock external dependencies
- Test error conditions

## Debugging

### 1. VS Code Debug Configuration

Create `.vscode/launch.json`:
```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug Backend",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/src/backend/index.js",
      "env": {
        "NODE_ENV": "development"
      }
    },
    {
      "name": "Debug API",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/src/api/index.js",
      "env": {
        "NODE_ENV": "development",
        "API_PORT": "3001"
      }
    }
  ]
}
```

### 2. Logging Levels

```bash
# Set debug logging
LOG_LEVEL=debug npm run dev

# View specific module logs
DEBUG=backend:* npm run backend:dev
DEBUG=api:* npm run api:dev
```

### 3. Database Debugging

```bash
# View database with Prisma Studio
npm run db:studio

# Check database connection
npx prisma db pull
```

## Performance Optimization

### 1. Database Queries

- Use database indexes for frequently queried fields
- Implement pagination for large datasets
- Use connection pooling
- Monitor slow queries

### 2. API Performance

- Implement caching for frequently accessed data
- Use compression middleware
- Implement rate limiting
- Monitor response times

### 3. Memory Management

- Monitor memory usage in long-running processes
- Implement cleanup for completed jobs
- Use streaming for large data exports

## Contributing

1. Follow the established code style
2. Write comprehensive tests
3. Update documentation
4. Create meaningful commit messages
5. Submit pull requests for review

## Troubleshooting

### Common Development Issues

1. **Port conflicts**: Check if ports 3000/3001 are in use
2. **Database connection**: Verify DATABASE_URL in .env
3. **Module not found**: Run `npm install` after pulling changes
4. **Test failures**: Check test database setup and mocks

### Getting Help

- Check existing documentation
- Review test files for examples
- Use VS Code debugging tools
- Check logs for error details
