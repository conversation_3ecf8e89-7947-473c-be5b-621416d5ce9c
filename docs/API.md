# DSV Scraper API Documentation

This document provides comprehensive documentation for the DSV Scraper REST API.

## Base URL

```
http://localhost:3001/api
```

## Authentication

Currently, the API does not require authentication. In production, consider implementing:
- API keys
- JWT tokens
- OAuth 2.0

## Response Format

All API responses follow this standard format:

### Success Response
```json
{
  "success": true,
  "data": {...},
  "message": "Optional success message"
}
```

### Error Response
```json
{
  "error": "Error type",
  "message": "Detailed error message",
  "details": [...] // Optional validation details
}
```

## Rate Limiting

- **Window**: 15 minutes
- **Max Requests**: 1000 per window
- **Headers**: `X-RateLimit-Limit`, `X-RateLimit-Remaining`, `X-RateLimit-Reset`

## Endpoints

### Scraping Operations

#### Start Event Scraping
```http
POST /api/scraping/event
```

**Request Body:**
```json
{
  "eventName": "50m Freestyle",
  "ageGroup": "2015",
  "gender": "M",
  "regionId": 1,
  "season": "2025"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Event scraping completed",
  "jobId": "event_1234567890_abc123",
  "rankingsFound": 25,
  "timestamp": "2025-01-20T10:30:00.000Z"
}
```

#### Start Full Scraping
```http
POST /api/scraping/all
```

**Response:**
```json
{
  "success": true,
  "message": "Full scraping started",
  "jobId": "all_1234567890_def456",
  "stats": {
    "totalEvents": 10,
    "totalRankings": 500
  },
  "timestamp": "2025-01-20T10:30:00.000Z"
}
```

#### Get Scraping Job Status
```http
GET /api/scraping/status/{jobId}
```

**Response:**
```json
{
  "success": true,
  "jobId": "event_1234567890_abc123",
  "status": {
    "status": "completed",
    "startTime": "2025-01-20T10:30:00.000Z",
    "endTime": "2025-01-20T10:32:15.000Z",
    "results": {
      "rankingsFound": 25,
      "success": true
    }
  }
}
```

#### Get Active Scraping Jobs
```http
GET /api/scraping/jobs
```

**Response:**
```json
{
  "success": true,
  "activeJobs": [
    {
      "jobId": "event_1234567890_abc123",
      "type": "event",
      "status": "running",
      "startTime": "2025-01-20T10:30:00.000Z"
    }
  ],
  "count": 1
}
```

#### Get Scraping History
```http
GET /api/scraping/history?limit=20
```

**Query Parameters:**
- `limit` (optional): Number of records to return (1-100, default: 20)

**Response:**
```json
{
  "success": true,
  "history": [
    {
      "jobId": "event_1234567890_abc123",
      "type": "event",
      "status": "completed",
      "startTime": "2025-01-20T10:30:00.000Z",
      "endTime": "2025-01-20T10:32:15.000Z"
    }
  ],
  "count": 1
}
```

#### Cancel Scraping Job
```http
DELETE /api/scraping/jobs/{jobId}
```

**Response:**
```json
{
  "success": true,
  "message": "Job cancelled successfully",
  "jobId": "event_1234567890_abc123"
}
```

### Data Access

#### Get Rankings
```http
GET /api/data/rankings
```

**Query Parameters:**
- `eventId` (optional): Filter by event ID
- `regionId` (optional): Filter by region ID (1-18)
- `ageGroup` (optional): Filter by age group
- `gender` (optional): Filter by gender (M/W)
- `season` (optional): Filter by season
- `limit` (optional): Number of records (1-1000, default: 100)
- `page` (optional): Page number (default: 1)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "rank": 1,
      "swimmerName": "John Doe",
      "birthYear": 2015,
      "club": "Swimming Club",
      "time": "00:25.50",
      "ageGroup": "2015",
      "gender": "M",
      "event": {
        "name": "50m Freestyle"
      },
      "region": {
        "name": "Region 1"
      }
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 100,
    "total": 1000,
    "pages": 10
  }
}
```

#### Get Top Rankings
```http
GET /api/data/rankings/top?limit=10
```

**Query Parameters:**
- `eventId` (optional): Filter by event ID
- `regionId` (optional): Filter by region ID
- `ageGroup` (optional): Filter by age group
- `gender` (optional): Filter by gender
- `limit` (optional): Number of records (1-100, default: 10)

#### Get Events
```http
GET /api/data/events
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "50m Freestyle",
      "code": "50FR",
      "distance": 50,
      "stroke": "Freestyle"
    }
  ],
  "count": 1
}
```

#### Get Regions
```http
GET /api/data/regions
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Region 1",
      "code": "R1"
    }
  ],
  "count": 1
}
```

#### Get Statistics
```http
GET /api/data/statistics
```

**Response:**
```json
{
  "success": true,
  "data": {
    "totalRankings": 10000,
    "totalEvents": 50,
    "totalRegions": 18,
    "lastUpdate": "2025-01-20T10:30:00.000Z"
  }
}
```

#### Export CSV
```http
GET /api/data/export/csv
```

**Query Parameters:** Same as `/api/data/rankings`

**Response:** CSV file download

### Job Management

#### Get Scheduled Jobs
```http
GET /api/jobs/scheduled
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "name": "weekly_scraping",
      "cronExpression": "0 2 * * 1",
      "enabled": true,
      "description": "Weekly full scraping of all events",
      "lastRun": "2025-01-20T02:00:00.000Z",
      "runCount": 5
    }
  ],
  "count": 1
}
```

#### Execute Scheduled Job
```http
POST /api/jobs/scheduled/{jobName}/execute
```

**Response:**
```json
{
  "success": true,
  "message": "Job executed successfully",
  "jobName": "weekly_scraping",
  "result": {
    "duration": "120000ms",
    "stats": {...}
  }
}
```

#### Toggle Scheduled Job
```http
PUT /api/jobs/scheduled/{jobName}/toggle
```

**Request Body:**
```json
{
  "enabled": true
}
```

**Response:**
```json
{
  "success": true,
  "message": "Job enabled successfully",
  "jobName": "weekly_scraping",
  "enabled": true
}
```

#### Get Job History
```http
GET /api/jobs/history?limit=20
```

#### Start Cleanup Job
```http
POST /api/jobs/processing/cleanup
```

**Request Body:**
```json
{
  "olderThanDays": 365
}
```

### System Monitoring

#### System Health Check
```http
GET /api/system/health
```

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2025-01-20T10:30:00.000Z",
  "uptime": 12345,
  "services": {
    "database": { "status": "healthy" },
    "scraping": { "status": "healthy" },
    "dataProcessing": { "status": "healthy" },
    "jobScheduler": { "status": "healthy" }
  },
  "api": {
    "status": "healthy",
    "uptime": 12345,
    "memory": {...}
  }
}
```

#### System Status
```http
GET /api/system/status
```

#### System Configuration
```http
GET /api/system/config
```

#### System Metrics
```http
GET /api/system/metrics
```

## Error Codes

| Code | Description |
|------|-------------|
| 400 | Bad Request - Invalid input data |
| 404 | Not Found - Resource not found |
| 429 | Too Many Requests - Rate limit exceeded |
| 500 | Internal Server Error - Server error |
| 503 | Service Unavailable - Backend service unavailable |

## Examples

### Complete Scraping Workflow

1. **Start scraping:**
```bash
curl -X POST http://localhost:3001/api/scraping/event \
  -H "Content-Type: application/json" \
  -d '{"eventName":"50m Freestyle","ageGroup":"2015","gender":"M"}'
```

2. **Check status:**
```bash
curl http://localhost:3001/api/scraping/status/event_1234567890_abc123
```

3. **Get results:**
```bash
curl http://localhost:3001/api/data/rankings?eventId=50FR&limit=10
```

### Export Data

```bash
curl http://localhost:3001/api/data/export/csv?eventId=50FR > rankings.csv
```

### Monitor System

```bash
curl http://localhost:3001/api/system/health
```
